import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
// import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

// Design System Colors
const COLORS = {
  primary: '#8B5CF6',
  background: '#FAFAFA',
  backgroundGradientStart: '#8B5CF6',
  backgroundGradientEnd: '#1F2937',
  onPrimary: '#FFFFFF',
  surface: '#FFFFFF',
};

// Animated progress ring component
const ProgressRing = ({ progress }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const circleRef = useRef();
  const radius = 50;
  const strokeWidth = 6;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: progress,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  return (
    <View style={styles.progressRingContainer}>
      <Animated.View
        style={[
          styles.progressRing,
          {
            borderColor: COLORS.primary,
            borderWidth: strokeWidth,
          },
        ]}
      />
      <Animated.View
        style={[
          styles.progressRingFill,
          {
            borderColor: COLORS.onPrimary,
            borderWidth: strokeWidth,
            transform: [
              {
                rotate: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }),
              },
            ],
          },
        ]}
      />
    </View>
  );
};

// Morphing shapes animation
const MorphingShapes = () => {
  const morphAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const morphAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(morphAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(morphAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );

    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    );

    morphAnimation.start();
    scaleAnimation.start();

    return () => {
      morphAnimation.stop();
      scaleAnimation.stop();
    };
  }, []);

  const borderRadius = morphAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [8, 50, 8],
  });

  return (
    <Animated.View
      style={[
        styles.morphingShape,
        {
          backgroundColor: COLORS.primary,
          transform: [{ scale: scaleAnim }],
          borderRadius,
        },
      ]}
    />
  );
};

// Floating particles animation
const FloatingParticle = ({ delay }) => {
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: -100,
            duration: 3000,
            useNativeDriver: true,
          }),
        ]),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [delay]);

  return (
    <Animated.View
      style={[
        styles.particle,
        {
          backgroundColor: COLORS.onPrimary,
          opacity,
          transform: [{ translateY }],
        },
      ]}
    />
  );
};

const LoadingScreen = ({
  visible = true,
  message = 'جاري المعالجة...',
  submessage = 'يرجى الانتظار',
  progress = 0,
  showProgress = false,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -30,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.overlay,
        {
          opacity: fadeAnim,
        },
      ]}
    >
      <LinearGradient
        colors={[
          COLORS.backgroundGradientStart + 'E6',
          COLORS.backgroundGradientEnd + 'E6',
        ]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            {/* Floating particles */}
            <View style={styles.particlesContainer}>
              {[...Array(6)].map((_, index) => (
                <FloatingParticle
                  key={index}
                  delay={index * 500}
                />
              ))}
            </View>

            {/* Main loading animation */}
            <View style={styles.animationContainer}>
              {showProgress ? (
                <ProgressRing progress={progress} />
              ) : (
                <MorphingShapes />
              )}
            </View>

            {/* Loading text */}
            <Animated.View
              style={[
                styles.textContainer,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <Text style={[styles.loadingMessage, { color: COLORS.onPrimary }]}>
                {message}
              </Text>
              <Text style={[styles.loadingSubmessage, { color: COLORS.onPrimary }]}>
                {submessage}
              </Text>

              {showProgress && (
                <Text style={[styles.progressText, { color: COLORS.onPrimary }]}>
                  {Math.round(progress * 100)}%
                </Text>
              )}
            </Animated.View>

            {/* Animated dots */}
            <View style={styles.dotsContainer}>
              {[0, 1, 2].map((index) => (
                <AnimatedDot key={index} delay={index * 200} />
              ))}
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </Animated.View>
  );
};

// Animated dot component
const AnimatedDot = ({ delay }) => {
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const opacityAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 0.5,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0.3,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [delay]);

  return (
    <Animated.View
      style={[
        styles.dot,
        {
          backgroundColor: COLORS.onPrimary,
          opacity: opacityAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  particlesContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'space-around',
    alignItems: 'center',
    flexDirection: 'row',
  },
  particle: {
    width: 4,
    height: 4,
    borderRadius: 2,
    position: 'absolute',
  },
  animationContainer: {
    marginBottom: 40,
  },
  progressRingContainer: {
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRing: {
    width: 100,
    height: 100,
    borderRadius: 50,
    position: 'absolute',
    opacity: 0.3,
  },
  progressRingFill: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  morphingShape: {
    width: 80,
    height: 80,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  loadingMessage: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  loadingSubmessage: {
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 16,
  },
  progressText: {
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default LoadingScreen;
