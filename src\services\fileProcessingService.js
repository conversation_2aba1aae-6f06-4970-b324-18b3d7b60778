// Enhanced File Processing Service for TestMeApp
// Supports delayed AI processing with user-controlled analysis

import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import fileCacheService from './fileCacheService';
import fileHashUtils from '../utils/fileHashUtils';
import AIService from './aiService';

class FileProcessingService {
  constructor() {
    this.isWeb = Platform.OS === 'web';
    this.supportedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];
  }

  // Phase 1: File Upload and Caching (No AI Processing)
  async uploadAndCacheFile(options = {}) {
    try {
      console.log('=== PHASE 1: FILE UPLOAD AND CACHING ===');
      
      // Step 1: Pick file
      const file = await this.pickFile(options);
      if (!file) {
        return { success: false, error: 'لم يتم اختيار ملف' };
      }

      // Step 2: Validate file
      const validation = fileHashUtils.validateFile(file);
      if (!validation.isValid) {
        return { 
          success: false, 
          error: validation.errors.join('\n'),
          warnings: validation.warnings 
        };
      }

      // Step 3: Generate file fingerprint
      const fingerprint = await fileHashUtils.generateFileFingerprint(file);
      
      // Step 4: Check for duplicates
      const isDuplicate = await fileCacheService.isDuplicateFile(fingerprint.composite);
      if (isDuplicate) {
        const cachedContent = await fileCacheService.getCachedFileContent(fingerprint.composite);
        return {
          success: true,
          isDuplicate: true,
          file: {
            hash: fingerprint.composite,
            name: file.name,
            size: file.size,
            type: file.type || file.mimeType,
            fingerprint
          },
          cachedContent,
          message: 'تم العثور على الملف في الذاكرة المؤقتة'
        };
      }

      // Step 5: Extract text content (without AI processing)
      const extractedText = await this.extractTextContent(file);
      if (!extractedText) {
        return { 
          success: false, 
          error: 'فشل في استخراج النص من الملف' 
        };
      }

      // Step 6: Cache file content (without AI-generated questions)
      await fileCacheService.cacheFileContent(
        fingerprint.composite,
        file,
        extractedText,
        null // No AI processing yet
      );

      console.log('File uploaded and cached successfully');
      return {
        success: true,
        isDuplicate: false,
        file: {
          hash: fingerprint.composite,
          name: file.name,
          size: file.size,
          type: file.type || file.mimeType,
          fingerprint
        },
        extractedText,
        message: 'تم رفع الملف وحفظه بنجاح'
      };

    } catch (error) {
      console.error('Error in uploadAndCacheFile:', error);
      return { 
        success: false, 
        error: `خطأ في رفع الملف: ${error.message}` 
      };
    }
  }

  // Phase 2: AI Processing with User Context
  async processFileWithAI(fileHash, userContext, processingType = 'quiz') {
    try {
      console.log('=== PHASE 2: AI PROCESSING WITH CONTEXT ===');
      console.log('Processing type:', processingType);
      console.log('User context:', userContext);

      // Step 1: Get cached file content
      const cachedContent = await fileCacheService.getCachedFileContent(fileHash);
      if (!cachedContent) {
        return { 
          success: false, 
          error: 'لم يتم العثور على الملف في الذاكرة المؤقتة' 
        };
      }

      // Step 2: Check if AI processing already exists for this context
      if (cachedContent.generatedQuestions && processingType === 'quiz') {
        return {
          success: true,
          isFromCache: true,
          result: cachedContent.generatedQuestions,
          message: 'تم استخدام الأسئلة المحفوظة مسبقاً'
        };
      }

      // Step 3: Prepare AI prompt based on processing type and user context
      const prompt = this.buildAIPrompt(cachedContent.extractedText, userContext, processingType);

      // Step 4: Call AI service
      const aiResult = await AIService.callAIModel(prompt);
      if (!aiResult.success) {
        return { 
          success: false, 
          error: `فشل في معالجة الملف بالذكاء الاصطناعي: ${aiResult.error}` 
        };
      }

      // Step 5: Process AI response based on type
      let processedResult;
      if (processingType === 'quiz') {
        processedResult = this.parseQuizResponse(aiResult.response);
      } else if (processingType === 'chat') {
        processedResult = aiResult.response;
      } else {
        processedResult = aiResult.response;
      }

      // Step 6: Update cache with AI results
      if (processingType === 'quiz') {
        await fileCacheService.cacheFileContent(
          fileHash,
          {
            name: cachedContent.filename,
            size: cachedContent.fileSize,
            type: cachedContent.fileType
          },
          cachedContent.extractedText,
          processedResult
        );
      }

      console.log('AI processing completed successfully');
      return {
        success: true,
        isFromCache: false,
        result: processedResult,
        message: 'تم معالجة الملف بالذكاء الاصطناعي بنجاح'
      };

    } catch (error) {
      console.error('Error in processFileWithAI:', error);
      return { 
        success: false, 
        error: `خطأ في معالجة الملف: ${error.message}` 
      };
    }
  }

  // Build AI prompt based on processing type and user context
  buildAIPrompt(extractedText, userContext, processingType) {
    const baseContext = `النص المستخرج من الملف:\n\n${extractedText}\n\n`;
    
    if (processingType === 'quiz') {
      return `${baseContext}السياق المطلوب: ${userContext}\n\nقم بإنشاء أسئلة اختبار متنوعة (اختيار من متعدد، صح/خطأ، إكمال الفراغات) بناءً على النص المعطى والسياق المطلوب. اجعل الأسئلة باللغة العربية وقدم الإجابات الصحيحة.`;
    } else if (processingType === 'chat') {
      return `${baseContext}السؤال أو الطلب: ${userContext}\n\nأجب على السؤال أو نفذ الطلب بناءً على محتوى النص المعطى. استخدم اللغة العربية في الإجابة.`;
    } else {
      return `${baseContext}المطلوب: ${userContext}\n\nقم بمعالجة النص حسب المطلوب واستخدم اللغة العربية في الإجابة.`;
    }
  }

  // Parse quiz response from AI
  parseQuizResponse(aiResponse) {
    try {
      // Try to parse as JSON first
      if (aiResponse.includes('{') && aiResponse.includes('}')) {
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      }

      // Fallback: parse as text format
      const questions = [];
      const lines = aiResponse.split('\n').filter(line => line.trim());
      
      let currentQuestion = null;
      for (const line of lines) {
        if (line.includes('؟') || line.toLowerCase().includes('question')) {
          if (currentQuestion) {
            questions.push(currentQuestion);
          }
          currentQuestion = {
            question: line.trim(),
            options: [],
            correctAnswer: '',
            type: 'multiple_choice'
          };
        } else if (line.includes('أ)') || line.includes('ب)') || line.includes('ج)') || line.includes('د)')) {
          if (currentQuestion) {
            currentQuestion.options.push(line.trim());
          }
        } else if (line.includes('الإجابة') || line.includes('الصحيح')) {
          if (currentQuestion) {
            currentQuestion.correctAnswer = line.trim();
          }
        }
      }

      if (currentQuestion) {
        questions.push(currentQuestion);
      }

      return { questions };
    } catch (error) {
      console.error('Error parsing quiz response:', error);
      return { questions: [], rawResponse: aiResponse };
    }
  }

  // Pick file using document picker
  async pickFile(options = {}) {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: options.type || this.supportedTypes,
        copyToCacheDirectory: true,
        multiple: false,
      });

      if (result.canceled) {
        return null;
      }

      const file = result.assets[0];
      console.log('File picked:', fileHashUtils.generateFileSummary(file));
      return file;
    } catch (error) {
      console.error('Error picking file:', error);
      throw new Error('فشل في اختيار الملف');
    }
  }

  // Extract text content from file (without AI)
  async extractTextContent(file) {
    try {
      console.log('Extracting text content from file...');
      
      if (this.isWeb) {
        return await this.extractTextFromWebFile(file);
      } else {
        return await this.extractTextFromNativeFile(file);
      }
    } catch (error) {
      console.error('Error extracting text:', error);
      return null;
    }
  }

  // Extract text from web file
  async extractTextFromWebFile(file) {
    try {
      if (file.type === 'text/plain') {
        return await this.readTextFile(file);
      } else if (file.type === 'application/pdf') {
        // For now, return placeholder - PDF parsing would need additional library
        return `[محتوى PDF: ${file.name}]\nحجم الملف: ${fileHashUtils.formatFileSize(file.size)}\nيتطلب معالجة خاصة لاستخراج النص.`;
      } else if (file.type?.includes('image/')) {
        return `[صورة: ${file.name}]\nحجم الملف: ${fileHashUtils.formatFileSize(file.size)}\nيمكن معالجتها باستخدام الذكاء الاصطناعي لاستخراج النص.`;
      } else {
        return `[ملف: ${file.name}]\nنوع الملف: ${file.type}\nحجم الملف: ${fileHashUtils.formatFileSize(file.size)}`;
      }
    } catch (error) {
      console.error('Error extracting text from web file:', error);
      return null;
    }
  }

  // Extract text from native file
  async extractTextFromNativeFile(file) {
    try {
      if (file.type === 'text/plain') {
        const content = await FileSystem.readAsStringAsync(file.uri);
        return content;
      } else {
        return `[ملف: ${file.name}]\nنوع الملف: ${file.type}\nحجم الملف: ${fileHashUtils.formatFileSize(file.size)}\nمسار الملف: ${file.uri}`;
      }
    } catch (error) {
      console.error('Error extracting text from native file:', error);
      return null;
    }
  }

  // Read text file content
  async readTextFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  // Get processing status for a file
  async getFileProcessingStatus(fileHash) {
    try {
      const cachedContent = await fileCacheService.getCachedFileContent(fileHash);
      if (!cachedContent) {
        return { exists: false };
      }

      return {
        exists: true,
        hasExtractedText: !!cachedContent.extractedText,
        hasAIProcessing: !!cachedContent.generatedQuestions,
        filename: cachedContent.filename,
        fileSize: cachedContent.fileSize,
        processedAt: cachedContent.processedAt,
        lastAccessed: cachedContent.lastAccessed
      };
    } catch (error) {
      console.error('Error getting file processing status:', error);
      return { exists: false, error: error.message };
    }
  }
}

export default new FileProcessingService();
