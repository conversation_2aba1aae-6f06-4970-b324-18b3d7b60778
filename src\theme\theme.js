import { MD3LightTheme } from 'react-native-paper';

export const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    // Primary purple gradient colors
    primary: '#8B5CF6', // Main purple
    primaryContainer: '#EDE9FE',
    primaryLight: '#A78BFA',
    primaryDark: '#7C3AED',

    // Secondary black/dark colors
    secondary: '#1F2937', // Dark gray/black
    secondaryContainer: '#F9FAFB',
    secondaryLight: '#374151',
    secondaryDark: '#111827',

    // Tertiary accent colors
    tertiary: '#EC4899', // Pink accent
    tertiaryContainer: '#FCE7F3',
    tertiaryLight: '#F472B6',
    tertiaryDark: '#DB2777',

    // Surface and background
    surface: '#FFFFFF',
    surfaceVariant: '#F8FAFC',
    surfaceDisabled: '#F1F5F9',
    background: '#FAFAFA',
    backgroundGradientStart: '#8B5CF6',
    backgroundGradientEnd: '#1F2937',

    // Status colors
    success: '#10B981',
    successContainer: '#D1FAE5',
    warning: '#F59E0B',
    warningContainer: '#FEF3C7',
    error: '#EF4444',
    errorContainer: '#FEE2E2',
    info: '#3B82F6',
    infoContainer: '#DBEAFE',

    // Text colors
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#581C87',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#1F2937',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#831843',
    onSurface: '#1F2937',
    onSurfaceVariant: '#6B7280',
    onBackground: '#1F2937',
    onSuccess: '#FFFFFF',
    onWarning: '#FFFFFF',
    onError: '#FFFFFF',
    onInfo: '#FFFFFF',

    // Border and outline colors
    outline: '#D1D5DB',
    outlineVariant: '#E5E7EB',
    border: '#E5E7EB',
    borderLight: '#F3F4F6',

    // Shadow and overlay
    shadow: '#000000',
    scrim: '#000000',
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.2)',
    inverseSurface: '#1F2937',
    inverseOnSurface: '#F9FAFB',
    inversePrimary: '#A78BFA',
    elevation: {
      level0: 'transparent',
      level1: '#F8FAFC',
      level2: '#F1F5F9',
      level3: '#E2E8F0',
      level4: '#CBD5E1',
      level5: '#94A3B8',
    },

    // Card and component specific colors
    cardBackground: '#FFFFFF',
    cardShadow: 'rgba(0, 0, 0, 0.1)',
    cardBorder: '#F1F5F9',

    // Quiz specific colors
    quizCorrect: '#10B981',
    quizIncorrect: '#EF4444',
    quizSelected: '#8B5CF6',
    quizTimer: '#F59E0B',
    quizTimerDanger: '#EF4444',

    // Gradient definitions for components
    gradients: {
      primary: ['#8B5CF6', '#7C3AED'],
      secondary: ['#1F2937', '#111827'],
      accent: ['#EC4899', '#DB2777'],
      success: ['#10B981', '#059669'],
      warning: ['#F59E0B', '#D97706'],
      error: ['#EF4444', '#DC2626'],
    },
  },

  // Enhanced typography system
  fonts: {
    ...MD3LightTheme.fonts,
    default: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    displayLarge: {
      fontFamily: 'System',
      fontSize: 57,
      fontWeight: '700',
      lineHeight: 64,
      letterSpacing: -0.25,
    },
    displayMedium: {
      fontFamily: 'System',
      fontSize: 45,
      fontWeight: '700',
      lineHeight: 52,
      letterSpacing: 0,
    },
    displaySmall: {
      fontFamily: 'System',
      fontSize: 36,
      fontWeight: '600',
      lineHeight: 44,
      letterSpacing: 0,
    },
    headlineLarge: {
      fontFamily: 'System',
      fontSize: 32,
      fontWeight: '600',
      lineHeight: 40,
      letterSpacing: 0,
    },
    headlineMedium: {
      fontFamily: 'System',
      fontSize: 28,
      fontWeight: '600',
      lineHeight: 36,
      letterSpacing: 0,
    },
    headlineSmall: {
      fontFamily: 'System',
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32,
      letterSpacing: 0,
    },
    titleLarge: {
      fontFamily: 'System',
      fontSize: 22,
      fontWeight: '500',
      lineHeight: 28,
      letterSpacing: 0,
    },
    titleMedium: {
      fontFamily: 'System',
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 24,
      letterSpacing: 0.15,
    },
    titleSmall: {
      fontFamily: 'System',
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 20,
      letterSpacing: 0.1,
    },
    bodyLarge: {
      fontFamily: 'System',
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 24,
      letterSpacing: 0.5,
    },
    bodyMedium: {
      fontFamily: 'System',
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 20,
      letterSpacing: 0.25,
    },
    bodySmall: {
      fontFamily: 'System',
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 16,
      letterSpacing: 0.4,
    },
    labelLarge: {
      fontFamily: 'System',
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 20,
      letterSpacing: 0.1,
    },
    labelMedium: {
      fontFamily: 'System',
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 16,
      letterSpacing: 0.5,
    },
    labelSmall: {
      fontFamily: 'System',
      fontSize: 11,
      fontWeight: '500',
      lineHeight: 16,
      letterSpacing: 0.5,
    },
  },

  // Animation and timing configurations
  animation: {
    scale: 1.0,
    duration: {
      short: 150,
      medium: 300,
      long: 500,
    },
    easing: {
      standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
      decelerated: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
      accelerated: 'cubic-bezier(0.4, 0.0, 1, 1)',
    },
  },

  // Shadow system
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};
