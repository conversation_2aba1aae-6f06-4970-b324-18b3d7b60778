import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import {
  Text,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';

const HistoryScreen = ({ navigation }) => {
  const theme = useTheme();
  const [history, setHistory] = useState([
    {
      id: '1',
      subject: 'Arabic Literature',
      score: 80,
      totalQuestions: 10,
      correctAnswers: 8,
      completedAt: '15 Oct 2023',
    },
    {
      id: '2',
      subject: 'Islamic History',
      score: 60,
      totalQuestions: 10,
      correctAnswers: 6,
      completedAt: '12 Oct 2023',
    },
    {
      id: '3',
      subject: 'Modern Arabic Poetry',
      score: 90,
      totalQuestions: 10,
      correctAnswers: 9,
      completedAt: '10 Oct 2023',
    },
    {
      id: '4',
      subject: 'Classical Arabic Grammar',
      score: 70,
      totalQuestions: 10,
      correctAnswers: 7,
      completedAt: '08 Oct 2023',
    },
    {
      id: '5',
      subject: 'Arabic Calligraphy',
      score: 50,
      totalQuestions: 10,
      correctAnswers: 5,
      completedAt: '05 Oct 2023',
    },
    {
      id: '6',
      subject: 'Arabic Philosophy',
      score: 100,
      totalQuestions: 10,
      correctAnswers: 10,
      completedAt: '02 Oct 2023',
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredHistory, setFilteredHistory] = useState([]);

  useFocusEffect(
    useCallback(() => {
      loadHistory();
    }, [])
  );

  useEffect(() => {
    filterHistory();
  }, [history, searchQuery]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyKey = 'quiz_history';
      const savedHistory = await AsyncStorage.getItem(historyKey);
      const historyData = savedHistory ? JSON.parse(savedHistory) : [];
      setHistory(historyData);
      setFilteredHistory(historyData);
    } catch (error) {
      console.error('Error loading history:', error);
      Alert.alert('خطأ', 'فشل في تحميل السجل');
    } finally {
      setLoading(false);
    }
  };

  const filterHistory = () => {
    if (!searchQuery || !searchQuery.trim()) {
      setFilteredHistory(history);
    } else {
      const filtered = history.filter(item =>
        item.subject.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredHistory(filtered);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHistory();
    setRefreshing(false);
  };

  const deleteHistoryItem = async (itemId) => {
    Alert.alert(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذا الاختبار من السجل؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedHistory = history.filter(item => item.id !== itemId);
              setHistory(updatedHistory);
              
              const historyKey = 'quiz_history';
              await AsyncStorage.setItem(historyKey, JSON.stringify(updatedHistory));
            } catch (error) {
              console.error('Error deleting history item:', error);
              Alert.alert('خطأ', 'فشل في حذف العنصر');
            }
          },
        },
      ]
    );
  };

  const clearAllHistory = async () => {
    Alert.alert(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف جميع الاختبارات من السجل؟ لا يمكن التراجع عن هذا الإجراء.',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف الكل',
          style: 'destructive',
          onPress: async () => {
            try {
              setHistory([]);
              const historyKey = 'quiz_history';
              await AsyncStorage.setItem(historyKey, JSON.stringify([]));
            } catch (error) {
              console.error('Error clearing history:', error);
              Alert.alert('خطأ', 'فشل في حذف السجل');
            }
          },
        },
      ]
    );
  };

  const viewQuizResults = (item) => {
    navigation.navigate('Results', { results: item.results });
  };

  const retakeQuiz = (item) => {
    navigation.navigate('Quiz', { quizData: item.results.quizData });
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getScoreColor = (score) => {
    if (score >= 80) return '#10B981'; // Green
    if (score >= 60) return '#F59E0B'; // Yellow
    return '#EF4444'; // Red
  };

  const getScoreEmoji = (score) => {
    if (score >= 90) return '🏆';
    if (score >= 80) return '🎉';
    if (score >= 70) return '👍';
    if (score >= 60) return '😊';
    return '💪';
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.centerContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            جاري تحميل السجل...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={28} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Quiz History</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Main Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {(filteredHistory.length > 0 ? filteredHistory : history).map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.historyCard}
              onPress={() => viewQuizResults(item)}
            >
              <View style={styles.cardContent}>
                <View style={styles.cardInfo}>
                  <Text style={styles.cardTitle}>{item.subject}</Text>
                  <View style={styles.cardDetails}>
                    <MaterialIcons name="calendar-today" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.cardDate}>{item.completedAt}</Text>
                    <Text style={styles.cardSeparator}>·</Text>
                    <Text style={styles.cardScore}>Score: {item.correctAnswers}/{item.totalQuestions}</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => deleteHistoryItem(item.id)}
                >
                  <MaterialIcons name="delete" size={24} color={COLORS.textSecondary} />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>


      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
    paddingRight: 40,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 100,
    gap: 12,
  },
  historyCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  cardInfo: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  cardDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  cardDate: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  cardSeparator: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginHorizontal: 2,
  },
  cardScore: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  deleteButton: {
    padding: 8,
    marginRight: -8,
  },

});

export default HistoryScreen;
