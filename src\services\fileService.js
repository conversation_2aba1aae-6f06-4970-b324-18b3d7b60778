// File Processing Service for Test Me App
// Handles PDF text extraction and image OCR

import { Platform } from 'react-native';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize GoogleGenAI with API key
const genAI = new GoogleGenerativeAI('AIzaSyAnEVS8kvCbbLQcD5bkZjbgzFTTO4A6CCI');

// AI Models for file processing
const FILE_PROCESSING_MODELS = [
  {
    name: 'Gemini 2.5 Flash Preview',
    type: 'sdk',
    model: 'gemini-2.5-flash-preview-05-20',
    priority: 1
  },
  {
    name: 'Gemini 1.5 Flash',
    type: 'sdk',
    model: 'gemini-1.5-flash',
    priority: 2
  },
  {
    name: 'Claude 3 Haiku',
    type: 'openrouter',
    endpoint: 'https://openrouter.ai/api/v1/chat/completions',
    model: 'anthropic/claude-3-haiku',
    apiKey: '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',
    priority: 3
  },
  {
    name: 'Gemini 1.5 Flash (OpenRouter)',
    type: 'openrouter',
    endpoint: 'https://openrouter.ai/api/v1/chat/completions',
    model: 'google/gemini-flash-1.5',
    apiKey: '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',
    priority: 4
  },
  {
    name: 'DeepSeek',
    type: 'deepseek',
    endpoint: 'https://api.deepseek.com/v1/chat/completions',
    model: 'deepseek-chat',
    apiKey: '***********************************',
    priority: 5
  }
];

class FileService {
  constructor() {
    this.supportedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
    this.supportedDocTypes = ['pdf'];
    this.isWeb = Platform.OS === 'web';
  }

  async processFile(file) {
    try {
      console.log('=== PROCESSING FILE ===');
      console.log('File object:', JSON.stringify(file, null, 2));

      if (!file || !file.uri) {
        throw new Error('ملف غير صالح');
      }

      const fileExtension = this.getFileExtension(file.name || file.uri);
      console.log('File extension detected:', fileExtension);

      if (this.supportedImageTypes.includes(fileExtension.toLowerCase())) {
        console.log('Processing as image...');
        return await this.processImage(file);
      } else if (this.supportedDocTypes.includes(fileExtension.toLowerCase())) {
        console.log('Processing as PDF...');
        return await this.processPDF(file);
      } else {
        throw new Error('نوع الملف غير مدعوم');
      }
    } catch (error) {
      console.error('Error processing file:', error);
      throw new Error('فشل في معالجة الملف: ' + error.message);
    }
  }

  getFileExtension(filename) {
    return filename.split('.').pop() || '';
  }

  async readFileAsBase64Web(fileObj) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        try {
          // Remove the data URL prefix to get just the base64 data
          const result = reader.result;
          if (typeof result === 'string' && result.includes(',')) {
            const base64 = result.split(',')[1];
            console.log('File read successfully, base64 length:', base64.length);
            resolve(base64);
          } else {
            reject(new Error('تنسيق الملف غير صحيح'));
          }
        } catch (error) {
          reject(new Error('فشل في معالجة الملف'));
        }
      };
      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));

      // Use the actual file object for web
      const actualFile = fileObj.file || fileObj;
      console.log('Reading file:', actualFile.name, 'Size:', actualFile.size);
      reader.readAsDataURL(actualFile);
    });
  }

  async processImage(file) {
    try {
      console.log('=== PROCESSING IMAGE ===');
      console.log('Image file details:', {
        name: file.name,
        size: file.size,
        uri: file.uri,
        type: file.type
      });

      let imageData;

      if (this.isWeb) {
        console.log('Using web FileReader API...');
        // For web, use FileReader API
        imageData = await this.readFileAsBase64Web(file);
      } else {
        console.log('Using native expo-file-system...');
        // For native, use expo-file-system
        const FileSystem = require('expo-file-system');
        imageData = await FileSystem.readAsStringAsync(file.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
      }

      console.log('Image data extracted, length:', imageData.length);

      // For now, we'll simulate OCR processing
      console.log('Starting OCR processing...');
      const extractedText = await this.simulateOCR(imageData);
      console.log('OCR completed, extracted text length:', extractedText.length);

      const result = {
        type: 'image',
        content: extractedText,
        metadata: {
          filename: file.name,
          size: file.size,
          processedAt: new Date().toISOString(),
        }
      };

      console.log('Image processing completed:', result.metadata);
      return result;
    } catch (error) {
      console.error('Error processing image:', error);
      throw new Error('فشل في معالجة الصورة');
    }
  }

  async processPDF(file) {
    try {
      console.log('=== PROCESSING PDF ===');
      console.log('PDF file details:', {
        name: file.name,
        size: file.size,
        uri: file.uri,
        type: file.type
      });

      let pdfData;

      if (this.isWeb) {
        console.log('Using web FileReader API for PDF...');
        // For web, use FileReader API
        pdfData = await this.readFileAsBase64Web(file);
      } else {
        console.log('Using native expo-file-system for PDF...');
        // For native, use expo-file-system
        const FileSystem = require('expo-file-system');
        pdfData = await FileSystem.readAsStringAsync(file.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
      }

      console.log('PDF data extracted, length:', pdfData.length);

      // For now, we'll simulate PDF text extraction
      console.log('Starting PDF text extraction...');
      const extractedText = await this.simulatePDFExtraction(pdfData);
      console.log('PDF extraction completed, extracted text length:', extractedText.length);

      const result = {
        type: 'pdf',
        content: extractedText,
        metadata: {
          filename: file.name,
          size: file.size,
          processedAt: new Date().toISOString(),
        }
      };

      console.log('PDF processing completed:', result.metadata);
      return result;
    } catch (error) {
      console.error('Error processing PDF:', error);
      throw new Error('فشل في معالجة ملف PDF');
    }
  }

  async simulateOCR(imageData) {
    const prompt = "Extract all text from this image. Write the text exactly as it appears, maintaining the original language (Arabic, English, or any other language). If the image contains equations or symbols, write them as they are. Do not add any interpretations or comments, just the extracted text. Keep the original language of the content - do not translate anything.";

    // Try models in priority order
    for (const model of FILE_PROCESSING_MODELS) {
      for (let attempt = 1; attempt <= 1; attempt++) {
        try {
          console.log(`Attempting OCR with ${model.name} (attempt ${attempt}/1)...`);

          let extractedText = '';

          if (model.type === 'sdk') {
            // Use GoogleGenAI SDK
            const aiModel = genAI.getGenerativeModel({
              model: model.model,
              generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.9,
                maxOutputTokens: 8192,
                candidateCount: 1,
              },
            });

            const result = await aiModel.generateContent([
              prompt,
              {
                inlineData: {
                  mimeType: "image/jpeg",
                  data: imageData
                }
              }
            ]);

            extractedText = result.response.text();
          } else if (model.type === 'rest') {
            // Use REST API for Gemini
            const response = await fetch(`${model.endpoint}?key=${model.apiKey}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                contents: [{
                  parts: [
                    { text: prompt },
                    {
                      inlineData: {
                        mimeType: "image/jpeg",
                        data: imageData
                      }
                    }
                  ]
                }],
                generationConfig: {
                  temperature: 0.1,
                  topK: 32,
                  topP: 0.9,
                  maxOutputTokens: 8192,
                  candidateCount: 1,
                }
              })
            });

            if (response.ok) {
              const data = await response.json();
              extractedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
            } else {
              throw new Error(`API error: ${response.status}`);
            }
          } else if (model.type === 'openrouter') {
            // Use OpenRouter API
            const response = await fetch(model.endpoint, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${model.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://testme-app.com',
                'X-Title': 'Test Me App',
              },
              body: JSON.stringify({
                model: model.model,
                messages: [{
                  role: 'user',
                  content: [
                    { type: 'text', text: prompt },
                    {
                      type: 'image_url',
                      image_url: { url: `data:image/jpeg;base64,${imageData}` }
                    }
                  ]
                }],
                max_tokens: 8192
              })
            });

            if (response.ok) {
              const data = await response.json();
              extractedText = data.choices?.[0]?.message?.content;
            } else {
              throw new Error(`API error: ${response.status}`);
            }
          } else if (model.type === 'deepseek') {
            // DeepSeek doesn't support images, skip
            console.log(`${model.name} doesn't support images, skipping...`);
            break;
          }

          // Validate extracted text
          if (extractedText && extractedText.trim().length > 10) {
            console.log(`OCR extracted text successfully with ${model.name}, length:`, extractedText.length);
            console.log('OCR text preview:', extractedText.substring(0, 200) + '...');
            return extractedText.trim();
          } else {
            console.warn(`${model.name} returned empty or very short text:`, extractedText);
          }
        } catch (error) {
          console.error(`${model.name} OCR API error (attempt ${attempt}):`, error);
          if (attempt < 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }
      }
    }

    // If all models failed, throw an error
    console.error('All OCR models failed to extract text from image');
    throw new Error('فشل في استخراج النص من الصورة. يرجى المحاولة مرة أخرى أو استخدام صورة أوضح.');
  }

  async simulatePDFExtraction(pdfData) {
    const prompt = "Extract all text from this PDF file. Write the text exactly as it appears, maintaining the original language (Arabic, English, or any other language). Preserve formatting, headings, and paragraphs. If there are equations or symbols, write them as they are. Do not add any interpretations or comments, just the extracted text. Keep the original language of the content - do not translate anything.";

    // Try models in priority order
    for (const model of FILE_PROCESSING_MODELS) {
      for (let attempt = 1; attempt <= 1; attempt++) {
        try {
          console.log(`Attempting PDF extraction with ${model.name} (attempt ${attempt}/1)...`);

          let extractedText = '';

          if (model.type === 'sdk') {
            // Use GoogleGenAI SDK
            const aiModel = genAI.getGenerativeModel({
              model: model.model,
              generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.9,
                maxOutputTokens: 8192,
                candidateCount: 1,
              },
            });

            const result = await aiModel.generateContent([
              prompt,
              {
                inlineData: {
                  mimeType: "application/pdf",
                  data: pdfData
                }
              }
            ]);

            extractedText = result.response.text();
          } else if (model.type === 'rest') {
            // Use REST API for Gemini
            const response = await fetch(`${model.endpoint}?key=${model.apiKey}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                contents: [{
                  parts: [
                    { text: prompt },
                    {
                      inlineData: {
                        mimeType: "application/pdf",
                        data: pdfData
                      }
                    }
                  ]
                }],
                generationConfig: {
                  temperature: 0.1,
                  topK: 32,
                  topP: 0.9,
                  maxOutputTokens: 8192,
                  candidateCount: 1,
                }
              })
            });

            if (response.ok) {
              const data = await response.json();
              extractedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
            } else {
              throw new Error(`API error: ${response.status}`);
            }
          } else if (model.type === 'openrouter') {
            // Use OpenRouter API
            const response = await fetch(model.endpoint, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${model.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://testme-app.com',
                'X-Title': 'Test Me App',
              },
              body: JSON.stringify({
                model: model.model,
                messages: [{
                  role: 'user',
                  content: [
                    { type: 'text', text: prompt },
                    {
                      type: 'image_url',
                      image_url: { url: `data:application/pdf;base64,${pdfData}` }
                    }
                  ]
                }],
                max_tokens: 8192
              })
            });

            if (response.ok) {
              const data = await response.json();
              extractedText = data.choices?.[0]?.message?.content;
            } else {
              throw new Error(`API error: ${response.status}`);
            }
          } else if (model.type === 'deepseek') {
            // DeepSeek doesn't support PDFs, skip
            console.log(`${model.name} doesn't support PDFs, skipping...`);
            break;
          }

          // Validate extracted text
          if (extractedText && extractedText.trim().length > 10) {
            console.log(`PDF extracted text successfully with ${model.name}, length:`, extractedText.length);
            console.log('PDF text preview:', extractedText.substring(0, 200) + '...');
            return extractedText.trim();
          } else {
            console.warn(`${model.name} returned empty or very short text:`, extractedText);
          }
        } catch (error) {
          console.error(`${model.name} PDF API error (attempt ${attempt}):`, error);
          if (attempt < 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }
      }
    }

    // If all models failed, throw an error
    console.error('All PDF extraction models failed');
    throw new Error('فشل في استخراج النص من ملف PDF. يرجى المحاولة مرة أخرى أو استخدام ملف آخر.');
  }

  async extractTextFromImage(imageUri) {
    try {
      // This would integrate with a real OCR service
      // For now, return mock data
      return await this.simulateOCR(imageUri);
    } catch (error) {
      console.error('Error extracting text from image:', error);
      throw new Error('فشل في استخراج النص من الصورة');
    }
  }

  async extractTextFromPDF(pdfUri) {
    try {
      // This would integrate with a real PDF processing service
      // For now, return mock data
      return await this.simulatePDFExtraction(pdfUri);
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      throw new Error('فشل في استخراج النص من ملف PDF');
    }
  }

  validateFile(file) {
    if (!file || !file.uri) {
      return { valid: false, error: 'ملف غير صالح' };
    }

    const fileExtension = this.getFileExtension(file.name || file.uri);
    const allSupportedTypes = [...this.supportedImageTypes, ...this.supportedDocTypes];
    
    if (!allSupportedTypes.includes(fileExtension.toLowerCase())) {
      return { 
        valid: false, 
        error: `نوع الملف غير مدعوم. الأنواع المدعومة: ${allSupportedTypes.join(', ')}` 
      };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size && file.size > maxSize) {
      return { 
        valid: false, 
        error: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت' 
      };
    }

    return { valid: true };
  }

  getFileInfo(file) {
    const extension = this.getFileExtension(file.name || file.uri);
    const type = this.supportedImageTypes.includes(extension.toLowerCase()) ? 'image' : 'document';
    
    return {
      name: file.name || 'ملف غير معروف',
      size: file.size || 0,
      type,
      extension: extension.toUpperCase(),
      uri: file.uri,
    };
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default new FileService();
