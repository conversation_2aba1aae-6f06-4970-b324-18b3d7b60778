import React, { useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Animated,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  FAB,
  Surface,
  Text,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path, Circle, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';

const { width, height } = Dimensions.get('window');

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';
import UnifiedCard from '../components/UnifiedCard';
import UnifiedButton from '../components/UnifiedButton';

// Enhanced animated icon wrapper
const AnimatedIconWrapper = ({ children, delay = 0 }) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.delay(delay),
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, [delay]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={{
        transform: [
          { scale: scaleAnim },
          { rotate },
        ],
      }}
    >
      {children}
    </Animated.View>
  );
};

// Enhanced SVG Icons with gradients
const QuizIcon = ({ size = 32, color = '#8B5CF6' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Defs>
      <SvgLinearGradient id="quizGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <Stop offset="0%" stopColor={color} />
        <Stop offset="100%" stopColor="#7C3AED" />
      </SvgLinearGradient>
    </Defs>
    <Path
      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      stroke="url(#quizGradient)"
      strokeWidth={2.5}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </Svg>
);

const HistoryIcon = ({ size = 32, color = '#1F2937' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Defs>
      <SvgLinearGradient id="historyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <Stop offset="0%" stopColor={color} />
        <Stop offset="100%" stopColor="#374151" />
      </SvgLinearGradient>
    </Defs>
    <Path
      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      stroke="url(#historyGradient)"
      strokeWidth={2.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const SettingsIcon = ({ size = 32, color = '#EC4899' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Defs>
      <SvgLinearGradient id="settingsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <Stop offset="0%" stopColor={color} />
        <Stop offset="100%" stopColor="#DB2777" />
      </SvgLinearGradient>
    </Defs>
    <Path
      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
      stroke="url(#settingsGradient)"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      stroke="url(#settingsGradient)"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ChatbotIcon = ({ size = 32, color = '#EC4899' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Defs>
      <SvgLinearGradient id="chatbotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <Stop offset="0%" stopColor={color} />
        <Stop offset="100%" stopColor="#DB2777" />
      </SvgLinearGradient>
    </Defs>
    <Path
      d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
      stroke="url(#chatbotGradient)"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <Circle
      cx="9"
      cy="10"
      r="1"
      fill="url(#chatbotGradient)"
    />
    <Circle
      cx="15"
      cy="10"
      r="1"
      fill="url(#chatbotGradient)"
    />
    <Path
      d="M9.5 13a3.5 3.5 0 0 0 5 0"
      stroke="url(#chatbotGradient)"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </Svg>
);

// Enhanced menu card component
const MenuCard = ({ item, index, onPress }) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.delay(index * 200),
      Animated.parallel([
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, [index]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.menuCardContainer,
        {
          opacity: opacityAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        },
      ]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <UnifiedCard
          variant="interactive"
          padding="medium"
          style={styles.menuCard}
        >
          <View style={styles.cardIconContainer}>
            <View style={[styles.iconBackground, { backgroundColor: COLORS.iconBackground }]}>
              <AnimatedIconWrapper delay={index * 300}>
                {item.icon}
              </AnimatedIconWrapper>
            </View>
          </View>

          <View style={styles.cardContent}>
            <Text style={[styles.cardTitle, { color: COLORS.textPrimary }]}>
              {item.title}
            </Text>
            <Text style={[styles.cardSubtitle, { color: COLORS.textSecondary }]}>
              {item.subtitle}
            </Text>
          </View>

          <View style={styles.cardArrow}>
            <Svg width={20} height={20} viewBox="0 0 24 24" fill="none">
              <Path
                d="M9 18l6-6-6-6"
                stroke={COLORS.primary}
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
          </View>
        </UnifiedCard>
      </TouchableOpacity>
    </Animated.View>
  );
};

const HomeScreen = ({ navigation }) => {
  const theme = useTheme();
  const headerAnim = useRef(new Animated.Value(0)).current;
  const fabAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.timing(headerAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(fabAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const menuItems = [
    {
      title: 'إنشاء اختبار جديد',
      subtitle: 'ابدأ اختباراً جديداً من ملف PDF أو صورة',
      icon: <QuizIcon size={40} color={COLORS.primary} />,
      onPress: () => navigation.navigate('QuizSetup'),
      color: COLORS.primary,
      category: 'main',
    },
    {
      title: 'سجل الاختبارات',
      subtitle: 'راجع الاختبارات السابقة والنتائج',
      icon: <HistoryIcon size={40} color={COLORS.secondary} />,
      onPress: () => navigation.navigate('History'),
      color: COLORS.secondary,
      category: 'main',
    },
    {
      title: 'المساعد الذكي',
      subtitle: 'احصل على مساعدة ذكية وإجابات فورية',
      icon: <ChatbotIcon size={40} color={COLORS.tertiary} />,
      onPress: () => navigation.navigate('Chatbot'),
      color: COLORS.tertiary,
      category: 'main',
    },
    {
      title: 'الإعدادات',
      subtitle: 'إعدادات التطبيق ومفتاح API والمزيد',
      icon: <SettingsIcon size={40} color={COLORS.primary} />,
      onPress: () => navigation.navigate('Settings'),
      color: COLORS.primary,
      category: 'secondary',
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: COLORS.background }]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={COLORS.backgroundGradientStart}
      />

      {/* Background Gradient */}
      <LinearGradient
        colors={[COLORS.backgroundGradientStart, COLORS.background]}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Enhanced Header Section */}
          <Animated.View
            style={[
              styles.headerContainer,
              {
                opacity: headerAnim,
                transform: [
                  {
                    translateY: headerAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-50, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <LinearGradient
              colors={[theme.colors.primary, theme.colors.primaryDark]}
              style={[styles.headerCard, theme.shadows.large]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.headerContent}>
                <View style={styles.logoContainer}>
                  <LinearGradient
                    colors={[theme.colors.onPrimary + '20', theme.colors.onPrimary + '10']}
                    style={styles.logoCircle}
                  >
                    <Text style={[styles.logoText, { color: theme.colors.onPrimary }]}>
                      اختبرني
                    </Text>
                  </LinearGradient>
                </View>

                <Text style={[styles.headerTitle, { color: COLORS.onPrimary }]}>
                  مرحباً بك في اختبرني
                </Text>
                <Text style={[styles.headerSubtitle, { color: COLORS.onPrimary }]}>
                  تطبيق الاختبارات الذكي المدعوم بالذكاء الاصطناعي
                </Text>
                <Text style={[styles.headerQuote, { color: COLORS.onPrimary }]}>
                  "التعلم رحلة لا تنتهي، وكل سؤال خطوة نحو المعرفة"
                </Text>
              </View>
            </LinearGradient>
          </Animated.View>

          {/* Enhanced Navigation Hub */}
          <View style={styles.navigationHub}>
            {/* Main Features Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>🚀 الميزات الرئيسية</Text>
              <View style={styles.mainFeaturesGrid}>
                {menuItems.filter(item => item.category === 'main').map((item, index) => (
                  <MenuCard
                    key={index}
                    item={item}
                    index={index}
                    onPress={item.onPress}
                  />
                ))}
              </View>
            </View>

            {/* Secondary Features Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>⚙️ إعدادات وأدوات</Text>
              <View style={styles.secondaryFeaturesContainer}>
                {menuItems.filter(item => item.category === 'secondary').map((item, index) => (
                  <MenuCard
                    key={`secondary-${index}`}
                    item={item}
                    index={index + 3}
                    onPress={item.onPress}
                  />
                ))}
              </View>
            </View>
          </View>

          {/* Enhanced Developer Info */}
          <Animated.View
            style={[
              styles.developerContainer,
              {
                opacity: headerAnim,
              },
            ]}
          >
            <LinearGradient
              colors={[theme.colors.surfaceVariant, theme.colors.surface]}
              style={[styles.developerCard, theme.shadows.small]}
            >
              <Text style={[styles.developerText, { color: theme.colors.onSurfaceVariant }]}>
                تطوير: أحمد عجينة (Ahmed Ajena)
              </Text>
              <Text style={[styles.developerEmail, { color: theme.colors.onSurfaceVariant }]}>
                <EMAIL>
              </Text>
            </LinearGradient>
          </Animated.View>
        </ScrollView>

        {/* Enhanced Floating Action Button */}
        <Animated.View
          style={[
            styles.fabContainer,
            {
              opacity: fabAnim,
              transform: [
                {
                  scale: fabAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                },
              ],
            },
          ]}
        >
          <LinearGradient
            colors={[theme.colors.primary, theme.colors.primaryDark]}
            style={[styles.fab, theme.shadows.large]}
          >
            <TouchableOpacity
              onPress={() => navigation.navigate('QuizSetup')}
              style={styles.fabButton}
            >
              <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
                <Path
                  d="M12 5v14m-7-7h14"
                  stroke={theme.colors.onPrimary}
                  strokeWidth={2.5}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </Svg>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height * 0.4,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  headerContainer: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
  },
  headerCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
  },
  headerContent: {
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 20,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 16,
    lineHeight: 24,
  },
  headerQuote: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    opacity: 0.8,
    lineHeight: 20,
  },
  navigationHub: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  sectionContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    ...TYPOGRAPHY.headlineSmall,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
    textAlign: 'right',
    paddingHorizontal: SPACING.xs,
  },
  mainFeaturesGrid: {
    gap: 16,
  },
  secondaryFeaturesContainer: {
    gap: 12,
  },
  menuCardContainer: {
    marginBottom: 20,
  },
  menuCard: {
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIconContainer: {
    marginRight: 20,
  },
  iconBackground: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 6,
    lineHeight: 24,
  },
  cardSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  cardArrow: {
    marginLeft: 12,
  },
  developerContainer: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  developerCard: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
  },
  developerText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  developerEmail: {
    fontSize: 12,
    opacity: 0.8,
    textAlign: 'center',
  },
  fabContainer: {
    position: 'absolute',
    bottom: 30,
    right: 20,
  },
  fab: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  fabButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeScreen;
