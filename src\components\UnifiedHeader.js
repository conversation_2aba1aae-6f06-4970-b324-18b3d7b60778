import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';

const UnifiedHeader = ({
  title,
  subtitle = null,
  showBackButton = true,
  onBackPress = null,
  rightComponent = null,
  variant = 'default', // default, minimal, elevated
  style = {},
  titleStyle = {},
  ...props
}) => {
  const getHeaderStyle = () => {
    const baseStyle = [styles.header];
    
    switch (variant) {
      case 'minimal':
        baseStyle.push(styles.headerMinimal);
        break;
      case 'elevated':
        baseStyle.push(styles.headerElevated);
        break;
      default:
        baseStyle.push(styles.headerDefault);
    }
    
    return baseStyle;
  };
  
  return (
    <View style={[...getHeaderStyle(), style]} {...props}>
      {/* Left Section - Back Button */}
      <View style={styles.leftSection}>
        {showBackButton && onBackPress && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBackPress}
            activeOpacity={0.7}
          >
            <MaterialIcons 
              name="arrow-back-ios" 
              size={24} 
              color={COLORS.textPrimary} 
            />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Center Section - Title */}
      <View style={styles.centerSection}>
        <Text style={[styles.title, titleStyle]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.subtitle}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {/* Right Section - Custom Component */}
      <View style={styles.rightSection}>
        {rightComponent || <View style={styles.spacer} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Base header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.background,
    minHeight: 56,
  },
  
  // Variant styles
  headerDefault: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerMinimal: {
    // No border or shadow
  },
  headerElevated: {
    ...SHADOWS.small,
    borderBottomWidth: 0,
  },
  
  // Section styles
  leftSection: {
    width: 48,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightSection: {
    width: 48,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  
  // Back button (restricted palette)
  backButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.iconBackground, // #1A102B
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
    ...SHADOWS.small,
  },
  
  // Text styles
  title: {
    ...TYPOGRAPHY.headlineMedium,
    color: COLORS.textPrimary,
    textAlign: 'center',
    fontWeight: '600',
  },
  subtitle: {
    ...TYPOGRAPHY.bodySmall,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  
  // Spacer for right section when no component
  spacer: {
    width: 40,
    height: 40,
  },
});

export default UnifiedHeader;
