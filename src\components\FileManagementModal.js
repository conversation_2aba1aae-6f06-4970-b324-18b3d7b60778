// File Management Modal for TestMeApp
// Comprehensive file metadata editing, tagging, and organization

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../theme/colors';
import sourcesRegistry from '../services/sourcesRegistry';

const FileManagementModal = ({ 
  visible, 
  onClose, 
  source,
  onUpdate 
}) => {
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState([]);
  const [newTag, setNewTag] = useState('');
  const [availableCategories, setAvailableCategories] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  useEffect(() => {
    if (visible && source) {
      loadFileData();
      loadAvailableOptions();
    }
  }, [visible, source]);

  const loadFileData = () => {
    setDescription(source.description || '');
    setCategory(source.category || 'uncategorized');
    setTags(source.tags || []);
    setIsFavorite(source.isFavorite || false);
  };

  const loadAvailableOptions = async () => {
    try {
      const [categories, allTags] = await Promise.all([
        sourcesRegistry.getAllCategories(),
        sourcesRegistry.getAllTags()
      ]);
      setAvailableCategories(categories);
      setAvailableTags(allTags);
    } catch (error) {
      console.error('Error loading options:', error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const metadata = {
        description: description.trim(),
        category,
        tags,
        isFavorite
      };

      const success = await sourcesRegistry.updateSourceMetadata(source.hash, metadata);
      
      if (success) {
        Alert.alert('نجح', 'تم حفظ التغييرات بنجاح');
        onUpdate && onUpdate({ ...source, ...metadata });
        onClose();
      } else {
        Alert.alert('خطأ', 'فشل في حفظ التغييرات');
      }
    } catch (error) {
      console.error('Error saving metadata:', error);
      Alert.alert('خطأ', 'حدث خطأ أثناء حفظ التغييرات');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    const trimmedTag = newTag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addSuggestedTag = (tag) => {
    if (!tags.includes(tag)) {
      setTags([...tags, tag]);
    }
  };

  const getFileTypeIcon = (fileType) => {
    if (fileType?.includes('pdf')) return 'picture-as-pdf';
    if (fileType?.includes('image')) return 'image';
    if (fileType?.includes('text')) return 'description';
    if (fileType?.includes('word')) return 'description';
    return 'insert-drive-file';
  };

  const CategoryChip = ({ categoryName, isSelected, onPress }) => (
    <TouchableOpacity
      style={[styles.categoryChip, isSelected && styles.categoryChipSelected]}
      onPress={() => onPress(categoryName)}
    >
      <Text style={[
        styles.categoryChipText,
        isSelected && styles.categoryChipTextSelected
      ]}>
        {categoryName}
      </Text>
    </TouchableOpacity>
  );

  const TagChip = ({ tag, onRemove, isRemovable = true }) => (
    <View style={styles.tagChip}>
      <Text style={styles.tagChipText}>{tag}</Text>
      {isRemovable && (
        <TouchableOpacity onPress={() => onRemove(tag)} style={styles.tagRemoveButton}>
          <MaterialIcons name="close" size={16} color={COLORS.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );

  if (!source) return null;

  const fileIcon = getFileTypeIcon(source.fileType);
  const suggestedTags = availableTags.filter(tag => !tags.includes(tag)).slice(0, 5);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          
          <Text style={styles.title}>إدارة الملف</Text>
          
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSave}
            disabled={loading}
          >
            <Text style={styles.saveButtonText}>
              {loading ? 'جاري الحفظ...' : 'حفظ'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* File Info */}
          <View style={styles.fileInfoSection}>
            <View style={styles.fileHeader}>
              <MaterialIcons name={fileIcon} size={32} color={COLORS.primary} />
              <View style={styles.fileDetails}>
                <Text style={styles.fileName}>{source.filename}</Text>
                <Text style={styles.fileSize}>
                  {source.formattedSize} • {source.formattedDate}
                </Text>
                <Text style={styles.fileStatus}>
                  {source.status === 'processed' ? '✅ معالج بالذكاء الاصطناعي' : '📁 محفوظ'}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.favoriteButton}
                onPress={() => setIsFavorite(!isFavorite)}
              >
                <MaterialIcons 
                  name={isFavorite ? "favorite" : "favorite-border"} 
                  size={24} 
                  color={isFavorite ? COLORS.error : COLORS.textSecondary} 
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>الوصف</Text>
            <TextInput
              style={styles.descriptionInput}
              placeholder="أضف وصفاً للملف..."
              placeholderTextColor={COLORS.textSecondary}
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {/* Category */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>الفئة</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.categoriesContainer}>
                {availableCategories.map(categoryName => (
                  <CategoryChip
                    key={categoryName}
                    categoryName={categoryName}
                    isSelected={category === categoryName}
                    onPress={setCategory}
                  />
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Tags */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>العلامات</Text>
            
            {/* Current Tags */}
            {tags.length > 0 && (
              <View style={styles.currentTagsContainer}>
                {tags.map(tag => (
                  <TagChip
                    key={tag}
                    tag={tag}
                    onRemove={removeTag}
                  />
                ))}
              </View>
            )}

            {/* Add New Tag */}
            <View style={styles.addTagContainer}>
              <TextInput
                style={styles.tagInput}
                placeholder="إضافة علامة جديدة..."
                placeholderTextColor={COLORS.textSecondary}
                value={newTag}
                onChangeText={setNewTag}
                onSubmitEditing={addTag}
              />
              <TouchableOpacity
                style={styles.addTagButton}
                onPress={addTag}
                disabled={!newTag.trim()}
              >
                <MaterialIcons name="add" size={20} color={COLORS.textPrimary} />
              </TouchableOpacity>
            </View>

            {/* Suggested Tags */}
            {suggestedTags.length > 0 && (
              <View style={styles.suggestedTagsSection}>
                <Text style={styles.suggestedTagsTitle}>علامات مقترحة:</Text>
                <View style={styles.suggestedTagsContainer}>
                  {suggestedTags.map(tag => (
                    <TouchableOpacity
                      key={tag}
                      style={styles.suggestedTag}
                      onPress={() => addSuggestedTag(tag)}
                    >
                      <Text style={styles.suggestedTagText}>{tag}</Text>
                      <MaterialIcons name="add" size={16} color={COLORS.primary} />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
          </View>

          {/* Usage Statistics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>إحصائيات الاستخدام</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <MaterialIcons name="visibility" size={20} color={COLORS.primary} />
                <Text style={styles.statText}>
                  استُخدم {source.usageCount || 0} مرة
                </Text>
              </View>
              <View style={styles.statItem}>
                <MaterialIcons name="access-time" size={20} color={COLORS.primary} />
                <Text style={styles.statText}>
                  آخر استخدام: {source.formattedDate}
                </Text>
              </View>
              {source.lastUsedIn && (
                <View style={styles.statItem}>
                  <MaterialIcons name="apps" size={20} color={COLORS.primary} />
                  <Text style={styles.statText}>
                    آخر استخدام في: {source.lastUsedIn}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Danger Zone */}
          <View style={styles.dangerSection}>
            <Text style={styles.dangerTitle}>منطقة الخطر</Text>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => {
                Alert.alert(
                  'تأكيد الحذف',
                  'هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء.',
                  [
                    { text: 'إلغاء', style: 'cancel' },
                    {
                      text: 'حذف',
                      style: 'destructive',
                      onPress: async () => {
                        const success = await sourcesRegistry.removeSource(source.hash);
                        if (success) {
                          Alert.alert('تم الحذف', 'تم حذف الملف بنجاح');
                          onUpdate && onUpdate(null); // Signal deletion
                          onClose();
                        } else {
                          Alert.alert('خطأ', 'فشل في حذف الملف');
                        }
                      }
                    }
                  ]
                );
              }}
            >
              <MaterialIcons name="delete" size={20} color={COLORS.error} />
              <Text style={styles.deleteButtonText}>حذف الملف</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.background,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  fileInfoSection: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  fileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileDetails: {
    flex: 1,
    marginLeft: 12,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  fileSize: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  fileStatus: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  favoriteButton: {
    padding: 8,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 12,
  },
  descriptionInput: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    padding: 12,
    fontSize: 16,
    color: COLORS.textPrimary,
    minHeight: 80,
  },
  categoriesContainer: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: COLORS.cardBackground,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginRight: 8,
  },
  categoryChipSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  categoryChipText: {
    fontSize: 14,
    color: COLORS.textPrimary,
  },
  categoryChipTextSelected: {
    color: COLORS.textPrimary,
    fontWeight: '600',
  },
  currentTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.cardBackground,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  tagChipText: {
    fontSize: 12,
    color: COLORS.textPrimary,
    marginRight: 4,
  },
  tagRemoveButton: {
    padding: 2,
  },
  addTagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tagInput: {
    flex: 1,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: COLORS.textPrimary,
    marginRight: 8,
  },
  addTagButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    padding: 10,
  },
  suggestedTagsSection: {
    marginTop: 8,
  },
  suggestedTagsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginBottom: 8,
  },
  suggestedTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  suggestedTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  suggestedTagText: {
    fontSize: 12,
    color: COLORS.primary,
    marginRight: 4,
  },
  statsContainer: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginLeft: 8,
  },
  dangerSection: {
    marginTop: 32,
    marginBottom: 32,
    padding: 16,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.error,
  },
  dangerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.error,
    marginBottom: 12,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 8,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: COLORS.error,
  },
  deleteButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.error,
    marginLeft: 8,
  },
});

export default FileManagementModal;
