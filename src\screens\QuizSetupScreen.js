import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  StatusBar,
  TextInput,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Chip,
  ProgressBar,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import LoadingScreen from '../components/LoadingScreen';
import aiService from '../services/aiService';
import fileService from '../services/fileService';
import fileProcessingService from '../services/fileProcessingService';
import sourcesRegistry from '../services/sourcesRegistry';
import { testLanguageDetection } from '../utils/testLanguageDetection';
import SourcesModal from '../components/SourcesModal';
import FileManagementModal from '../components/FileManagementModal';
import ProgressIndicator, { FILE_PROCESSING_STEPS, StatusBadge } from '../components/ProgressIndicator';

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';
import UnifiedHeader from '../components/UnifiedHeader';
import UnifiedCard from '../components/UnifiedCard';
import UnifiedButton from '../components/UnifiedButton';

const QuizSetupScreen = ({ navigation }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  const [progress, setProgress] = useState(0);

  // Form state with new defaults
  const [subject, setSubject] = useState('');
  const [questionType, setQuestionType] = useState('multiple_choice');
  const [questionCount, setQuestionCount] = useState('10'); // Default to 10
  const [contentType, setContentType] = useState('chapter');
  const [specialNotes, setSpecialNotes] = useState('');
  const [timerMinutes, setTimerMinutes] = useState('15'); // Default to 15 minutes
  const [selectedFile, setSelectedFile] = useState(null);

  // New state for delayed processing workflow
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedSource, setSelectedSource] = useState(null);
  const [processingContext, setProcessingContext] = useState('');
  const [showSourcesModal, setShowSourcesModal] = useState(false);
  const [availableSources, setAvailableSources] = useState([]);
  const [processingPhase, setProcessingPhase] = useState('setup'); // 'setup', 'upload', 'context', 'processing'
  const [questionLanguage, setQuestionLanguage] = useState('auto');
  const [selectedModel, setSelectedModel] = useState('auto');
  const [availableModels, setAvailableModels] = useState([]);

  // New state for enhanced features
  const [showPreview, setShowPreview] = useState(false);
  const [extractedContent, setExtractedContent] = useState('');
  const [contentAnalysis, setContentAnalysis] = useState(null);
  const [savedTemplates, setSavedTemplates] = useState([]);
  const [showTemplates, setShowTemplates] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState(0);
  const [detectedLanguage, setDetectedLanguage] = useState('');

  // Expandable sections state
  const [showContentScope, setShowContentScope] = useState(false);
  const [showLanguageOptions, setShowLanguageOptions] = useState(false);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const questionTypes = [
    { value: 'multiple_choice', label: 'اختيار من متعدد', icon: 'radio-button-checked', description: 'أسئلة بخيارات متعددة' },
    { value: 'true_false', label: 'صح أو خطأ', icon: 'check-circle', description: 'أسئلة بإجابة صح أو خطأ' },
    { value: 'short_answer', label: 'إجابة قصيرة', icon: 'edit', description: 'أسئلة تتطلب إجابة مكتوبة قصيرة' },
    { value: 'mixed', label: 'مختلط', icon: 'shuffle', description: 'مزيج من جميع الأنواع' },
  ];

  const contentTypes = [
    { value: 'chapter', label: 'فصل واحد', description: 'محتوى فصل دراسي واحد' },
    { value: 'semester', label: 'فصل دراسي', description: 'محتوى فصل دراسي كامل' },
    { value: 'book', label: 'كتاب كامل', description: 'محتوى كتاب دراسي كامل' },
    { value: 'exam', label: 'أسئلة امتحان', description: 'مراجعة لامتحان قادم' },
    { value: 'custom', label: 'مخصص', description: 'نطاق محدد حسب المحتوى' },
  ];

  const questionLanguages = [
    { value: 'auto', label: 'تلقائي (حسب المحتوى)', icon: 'autorenew', description: 'اكتشاف تلقائي للغة' },
    { value: 'ar', label: 'العربية', icon: 'language', description: 'أسئلة باللغة العربية' },
    { value: 'en', label: 'English', icon: 'language', description: 'Questions in English' },
    { value: 'fr', label: 'Français', icon: 'language', description: 'Questions en français' },
  ];



  // Load available AI models and initialize component
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        // Load AI models
        const models = aiService.getAvailableModels();
        setAvailableModels([
          { key: 'auto', name: 'تلقائي (أفضل نموذج متاح)', priority: 0 },
          ...models
        ]);

        // Load saved templates
        const templates = await loadSavedTemplates();
        setSavedTemplates(templates);

        // Animate component entrance
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true,
          }),
        ]).start();
      } catch (error) {
        console.error('Error initializing component:', error);
        setAvailableModels([{ key: 'auto', name: 'تلقائي (أفضل نموذج متاح)', priority: 0 }]);
      }
    };
    initializeComponent();
  }, []);

  // Update estimated time when inputs change
  useEffect(() => {
    calculateEstimatedTime();
  }, [questionCount, questionType, timerMinutes]);

  // Utility functions
  const loadSavedTemplates = async () => {
    try {
      // This would load from AsyncStorage in a real implementation
      return [];
    } catch (error) {
      console.error('Error loading templates:', error);
      return [];
    }
  };

  const calculateEstimatedTime = () => {
    const count = parseInt(questionCount) || 10;
    const timePerQuestion = questionType === 'multiple_choice' ? 1.5 :
                           questionType === 'true_false' ? 1 :
                           questionType === 'short_answer' ? 3 : 2;
    const estimated = Math.ceil(count * timePerQuestion);
    setEstimatedTime(estimated);
  };

  const analyzeContent = async (content) => {
    try {
      const wordCount = content.split(/\s+/).length;
      const estimatedReadingTime = Math.ceil(wordCount / 200); // 200 words per minute

      // Simple topic detection (in a real app, this would use AI)
      const topics = extractTopics(content);

      // Language detection
      const language = detectLanguage(content);
      setDetectedLanguage(language);

      return {
        wordCount,
        estimatedReadingTime,
        topics,
        language,
        complexity: wordCount > 1000 ? 'high' : wordCount > 500 ? 'medium' : 'low'
      };
    } catch (error) {
      console.error('Error analyzing content:', error);
      return null;
    }
  };

  const extractTopics = (content) => {
    // Simple keyword extraction (in a real app, this would use NLP)
    const words = content.toLowerCase().split(/\s+/);
    const commonWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'من', 'في', 'على', 'إلى', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي']);
    const wordFreq = {};

    words.forEach(word => {
      if (word.length > 3 && !commonWords.has(word)) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  };

  const detectLanguage = (content) => {
    const arabicPattern = /[\u0600-\u06FF]/;
    const englishPattern = /[a-zA-Z]/;

    const arabicMatches = content.match(arabicPattern);
    const englishMatches = content.match(englishPattern);

    if (arabicMatches && arabicMatches.length > (englishMatches?.length || 0)) {
      return 'ar';
    } else if (englishMatches) {
      return 'en';
    }
    return 'auto';
  };

  const pickDocument = async () => {
    try {
      setLoading(true);
      setProcessingStep('اختيار الملف...');

      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const file = result.assets[0];
        const fileData = {
          type: file.mimeType?.includes('pdf') ? 'pdf' : 'document',
          uri: file.uri,
          name: file.name,
          size: file.size,
          file: file.file || file,
        };

        setSelectedFile(fileData);

        // Process file immediately for preview
        await processFileForPreview(fileData);
      }
    } catch (error) {
      console.error('Document picker error:', error);
      Alert.alert('خطأ', 'فشل في اختيار الملف');
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      setLoading(true);
      setProcessingStep('اختيار الصورة...');

      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('تنبيه', 'نحتاج إذن للوصول إلى الصور');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const fileData = {
          type: 'image',
          uri: asset.uri,
          name: asset.fileName || 'صورة محددة',
          size: asset.fileSize || 0,
          file: asset.file || asset,
        };

        setSelectedFile(fileData);

        // Process image immediately for preview
        await processFileForPreview(fileData);
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('خطأ', 'فشل في اختيار الصورة');
    } finally {
      setLoading(false);
    }
  };

  const processFileForPreview = async (fileData) => {
    try {
      setProcessingStep('معالجة المحتوى...');
      setProgress(0.3);

      const processedFile = await fileService.processFile(fileData);
      setExtractedContent(processedFile.content);

      setProcessingStep('تحليل المحتوى...');
      setProgress(0.7);

      const analysis = await analyzeContent(processedFile.content);
      setContentAnalysis(analysis);

      setProgress(1);
      setProcessingStep('تم بنجاح!');

      // Auto-adjust settings based on content
      if (analysis) {
        if (analysis.language !== 'auto') {
          setQuestionLanguage(analysis.language);
        }

        // Suggest question count based on content length
        if (!questionCount || questionCount === '10') {
          const suggestedCount = Math.min(Math.max(Math.floor(analysis.wordCount / 100), 5), 50);
          setQuestionCount(suggestedCount.toString());
        }
      }

    } catch (error) {
      console.error('Error processing file for preview:', error);
      Alert.alert('خطأ', 'فشل في معالجة الملف');
    }
  };



  const saveTemplate = async () => {
    try {
      const template = {
        id: Date.now().toString(),
        name: subject || 'قالب جديد',
        subject,
        questionType,
        questionCount,
        contentType,
        timerMinutes,
        questionLanguage,
        specialNotes,
        createdAt: new Date().toISOString(),
      };

      const updatedTemplates = [...savedTemplates, template];
      setSavedTemplates(updatedTemplates);

      // In a real app, save to AsyncStorage
      Alert.alert('نجح', 'تم حفظ القالب بنجاح');
    } catch (error) {
      console.error('Error saving template:', error);
      Alert.alert('خطأ', 'فشل في حفظ القالب');
    }
  };

  const loadTemplate = (template) => {
    setSubject(template.subject);
    setQuestionType(template.questionType);
    setQuestionCount(template.questionCount);
    setContentType(template.contentType);
    setTimerMinutes(template.timerMinutes);
    setQuestionLanguage(template.questionLanguage);
    setSpecialNotes(template.specialNotes);
    setShowTemplates(false);
    Alert.alert('نجح', 'تم تحميل القالب بنجاح');
  };

  // New delayed processing functions
  const handleFileUploadOnly = async () => {
    try {
      setLoading(true);
      setProcessingStep('رفع الملف...');
      setProcessingPhase('upload');

      // Phase 1: Upload and cache file without AI processing
      const result = await fileProcessingService.uploadAndCacheFile();

      if (!result.success) {
        Alert.alert('خطأ', result.error);
        return;
      }

      if (result.isDuplicate) {
        Alert.alert(
          'ملف موجود',
          `${result.message}\nهل تريد استخدام الملف المحفوظ؟`,
          [
            { text: 'إلغاء', style: 'cancel' },
            {
              text: 'استخدام',
              onPress: () => {
                setSelectedSource(result.file);
                setProcessingPhase('context');
              }
            }
          ]
        );
      } else {
        setSelectedSource(result.file);
        setProcessingPhase('context');
        Alert.alert('نجح', result.message);
      }

      // Record usage in sources registry
      await sourcesRegistry.recordSourceUsage(result.file.hash, 'QuizSetup');

    } catch (error) {
      console.error('Error in file upload:', error);
      Alert.alert('خطأ', 'فشل في رفع الملف');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFromSources = async () => {
    try {
      setLoading(true);
      setProcessingStep('تحميل المصادر المحفوظة...');

      const sources = await sourcesRegistry.getRecentSources(20);
      setAvailableSources(sources);
      setShowSourcesModal(true);

    } catch (error) {
      console.error('Error loading sources:', error);
      Alert.alert('خطأ', 'فشل في تحميل المصادر المحفوظة');
    } finally {
      setLoading(false);
    }
  };

  const handleSourceSelection = (source) => {
    setSelectedSource(source);
    setShowSourcesModal(false);
    setProcessingPhase('context');

    // Record usage
    sourcesRegistry.recordSourceUsage(source.hash, 'QuizSetup');
  };

  const handleProcessWithAI = async () => {
    if (!selectedSource) {
      Alert.alert('تنبيه', 'يرجى اختيار ملف أولاً');
      return;
    }

    if (!processingContext.trim()) {
      Alert.alert('تنبيه', 'يرجى إدخال السياق أو التعليمات للذكاء الاصطناعي');
      return;
    }

    try {
      setLoading(true);
      setProcessingStep('معالجة الملف بالذكاء الاصطناعي...');
      setProcessingPhase('processing');

      // Build context from form data
      const fullContext = `
        المادة: ${subject}
        نوع الأسئلة: ${questionType}
        عدد الأسئلة: ${questionCount}
        نوع المحتوى: ${contentType}
        ملاحظات خاصة: ${specialNotes}
        السياق الإضافي: ${processingContext}
      `.trim();

      // Phase 2: Process with AI using user context
      const result = await fileProcessingService.processFileWithAI(
        selectedSource.hash,
        fullContext,
        'quiz'
      );

      if (!result.success) {
        Alert.alert('خطأ', result.error);
        return;
      }

      // Navigate to quiz with generated questions
      navigation.navigate('Quiz', {
        questions: result.result.questions || [],
        subject,
        timerMinutes: parseInt(timerMinutes),
        source: selectedSource,
        context: fullContext
      });

    } catch (error) {
      console.error('Error in AI processing:', error);
      Alert.alert('خطأ', 'فشل في معالجة الملف بالذكاء الاصطناعي');
    } finally {
      setLoading(false);
    }
  };

  const handleStartQuiz = async () => {
    // Enhanced validation
    if (!selectedFile) {
      Alert.alert('تنبيه', 'يرجى اختيار ملف أو صورة أولاً');
      return;
    }

    if (!subject.trim()) {
      Alert.alert('تنبيه', 'يرجى إدخال اسم المادة أو الموضوع');
      return;
    }

    // Validate question count - allow empty for smart generation
    if (questionCount && (isNaN(parseInt(questionCount)) || parseInt(questionCount) < 1)) {
      Alert.alert('خطأ', 'يرجى إدخال عدد صحيح للأسئلة أو اتركه فارغاً للتوليد الذكي');
      return;
    }

    // Validate timer
    if (!timerMinutes || isNaN(parseInt(timerMinutes)) || parseInt(timerMinutes) < 1) {
      Alert.alert('خطأ', 'يرجى إدخال وقت صحيح بالدقائق');
      return;
    }

    setLoading(true);
    setProgress(0);

    try {
      setProcessingStep('بدء معالجة الملف...');
      setProgress(0.1);

      // Use existing processed content if available, otherwise process now
      let processedFile;
      if (extractedContent) {
        processedFile = { content: extractedContent, type: selectedFile.type };
      } else {
        setProcessingStep('استخراج المحتوى من الملف...');
        processedFile = await fileService.processFile(selectedFile);
        setExtractedContent(processedFile.content);
      }

      setProgress(0.3);
      console.log('File processed successfully:', processedFile.type);
      console.log('Content length:', processedFile.content.length);

      // Validate content
      if (!processedFile.content || processedFile.content.trim().length < 20) {
        Alert.alert(
          'خطأ',
          'لم يتم استخراج محتوى كافٍ من الملف. يرجى التأكد من وضوح النص والمحاولة مرة أخرى.',
          [{ text: 'حسناً' }]
        );
        return;
      }

      setProcessingStep('تحليل المحتوى وتحديد الموضوعات...');
      setProgress(0.5);

      // Analyze content if not already done
      let analysis = contentAnalysis;
      if (!analysis) {
        analysis = await analyzeContent(processedFile.content);
        setContentAnalysis(analysis);
      }

      setProcessingStep('توليد الأسئلة بالذكاء الاصطناعي...');
      setProgress(0.7);

      // Determine question count - smart generation if empty
      let finalQuestionCount;
      if (!questionCount) {
        // Smart question generation based on content analysis
        if (analysis) {
          const contentComplexity = analysis.complexity;
          const baseCount = contentComplexity === 'high' ? 25 :
                           contentComplexity === 'medium' ? 15 : 10;
          finalQuestionCount = Math.min(Math.max(Math.floor(analysis.wordCount / 80), baseCount), 50);
        } else {
          finalQuestionCount = 'max';
        }
      } else {
        finalQuestionCount = parseInt(questionCount);
      }

      console.log('=== ENHANCED QUIZ GENERATION ===');
      console.log('Content analysis:', analysis);
      console.log('Final question count:', finalQuestionCount);
      console.log('Question type:', questionType);
      console.log('Language:', questionLanguage);

      // Generate questions with enhanced parameters
      const questions = await aiService.generateQuestions(processedFile.content, {
        subject,
        questionType,
        questionCount: finalQuestionCount,
        contentType,
        specialNotes,
        language: questionLanguage === 'auto' ? (analysis?.language || 'ar') : questionLanguage,
        selectedModel: selectedModel === 'auto' ? null : selectedModel,
        contentAnalysis: analysis,
        comprehensiveCoverage: !questionCount, // Enable comprehensive coverage for smart generation
      });

      setProgress(0.9);
      console.log('Questions generated successfully:', questions.length);

      if (!questions || questions.length === 0) {
        Alert.alert('خطأ', 'فشل في توليد الأسئلة. يرجى المحاولة مرة أخرى.');
        return;
      }

      setProcessingStep('إعداد الاختبار...');
      setProgress(1);

      const quizData = {
        subject,
        questionType,
        questionCount: questions.length,
        contentType,
        specialNotes,
        timerMinutes: parseInt(timerMinutes),
        questionLanguage: questionLanguage === 'auto' ? (analysis?.language || 'ar') : questionLanguage,
        isMaxQuestions: !questionCount,
        file: selectedFile,
        questions,
        processedFile,
        contentAnalysis: analysis,
        estimatedTime,
      };

      console.log('Navigating to quiz with', questions.length, 'questions');
      navigation.navigate('Quiz', { quizData });

    } catch (error) {
      console.error('Error creating quiz:', error);
      Alert.alert('خطأ', error.message || 'فشل في إنشاء الاختبار. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
      setProgress(0);
      setProcessingStep('');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      {/* Enhanced Loading Screen */}
      <LoadingScreen
        visible={loading}
        message={processingStep || "جاري إنشاء الاختبار..."}
        submessage="يرجى الانتظار، نحن نعالج المحتوى ونولد الأسئلة بذكاء"
        showProgress={true}
        progress={progress}
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Enhanced Header */}
        <Animated.View style={[styles.header, { opacity: fadeAnim }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back-ios" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>إعداد الاختبار</Text>
          <TouchableOpacity
            style={styles.templateButton}
            onPress={() => setShowTemplates(!showTemplates)}
          >
            <MaterialIcons name="bookmark" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </Animated.View>

        {/* Main Title with Animation */}
        <Animated.View style={[styles.titleContainer, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}>
          <Text style={styles.mainTitle}>إنشاء اختبار ذكي</Text>
          <Text style={styles.subtitle}>
            {selectedFile ? 'تم تحديد الملف - أكمل الإعدادات' : 'ابدأ بتحديد ملف أو صورة'}
          </Text>
        </Animated.View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Enhanced File Upload Section with Delayed Processing */}
          <Animated.View style={[styles.uploadSection, { opacity: fadeAnim }]}>
            <Text style={styles.sectionTitle}>📁 تحديد المحتوى</Text>

            {/* Workflow Progress Indicator */}
            <View style={styles.progressSection}>
              <ProgressIndicator
                steps={FILE_PROCESSING_STEPS.slice(0, 4)} // Show first 4 steps for quiz setup
                currentStep={
                  processingPhase === 'setup' ? 0 :
                  processingPhase === 'upload' ? 1 :
                  processingPhase === 'context' ? 2 :
                  processingPhase === 'processing' ? 3 : 0
                }
                size="small"
                showLabels={true}
              />
            </View>

            {processingPhase === 'setup' && (
              <View style={styles.uploadButtonsContainer}>
                <TouchableOpacity
                  style={styles.uploadButton}
                  onPress={handleFileUploadOnly}
                >
                  <View style={styles.uploadIconContainer}>
                    <MaterialIcons name="cloud-upload" size={28} color={COLORS.primary} />
                  </View>
                  <View style={styles.uploadTextContainer}>
                    <Text style={styles.uploadText}>رفع ملف جديد</Text>
                    <Text style={styles.uploadSubtext}>PDF, Word, Text, Images</Text>
                  </View>
                  <MaterialIcons name="chevron-left" size={24} color={COLORS.textSecondary} />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.uploadButton}
                  onPress={handleSelectFromSources}
                >
                  <View style={styles.uploadIconContainer}>
                    <MaterialIcons name="folder" size={28} color={COLORS.secondary} />
                  </View>
                  <View style={styles.uploadTextContainer}>
                    <Text style={styles.uploadText}>اختيار من المصادر المحفوظة</Text>
                    <Text style={styles.uploadSubtext}>الملفات المعالجة مسبقاً</Text>
                  </View>
                  <MaterialIcons name="chevron-left" size={24} color={COLORS.textSecondary} />
                </TouchableOpacity>
              </View>
            )}

            {/* Selected Source Display */}
            {selectedSource && (
              <Card style={styles.fileInfoCard}>
                <View style={styles.fileInfo}>
                  <MaterialIcons
                    name="description"
                    size={24}
                    color={COLORS.primary}
                  />
                  <View style={styles.fileDetails}>
                    <Text style={styles.fileName}>{selectedSource.name}</Text>
                    <Text style={styles.fileSize}>
                      {selectedSource.formattedSize || 'حجم غير معروف'}
                    </Text>
                    <Text style={styles.fileStatus}>
                      {selectedSource.status === 'processed' ? '✅ معالج بالذكاء الاصطناعي' : '📁 محفوظ'}
                    </Text>
                  </View>
                  <View style={styles.sourceActions}>
                    <TouchableOpacity onPress={() => setSelectedSource(null)}>
                      <MaterialIcons name="close" size={24} color={COLORS.error} />
                    </TouchableOpacity>
                  </View>
                </View>
              </Card>
            )}

            {/* Context Input Section for AI Processing */}
            {processingPhase === 'context' && selectedSource && (
              <Card style={styles.contextCard}>
                <Text style={styles.sectionTitle}>🤖 تعليمات الذكاء الاصطناعي</Text>
                <Text style={styles.contextDescription}>
                  أدخل السياق أو التعليمات الخاصة لمعالجة الملف بالذكاء الاصطناعي
                </Text>

                <TextInput
                  style={styles.contextInput}
                  placeholder="مثال: ركز على الفصل الثالث، اجعل الأسئلة متوسطة الصعوبة..."
                  placeholderTextColor={COLORS.textSecondary}
                  value={processingContext}
                  onChangeText={setProcessingContext}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />

                <View style={styles.contextActions}>
                  <TouchableOpacity
                    style={styles.contextButton}
                    onPress={() => setProcessingPhase('setup')}
                  >
                    <Text style={styles.contextButtonText}>رجوع</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.contextButton, styles.contextButtonPrimary]}
                    onPress={handleProcessWithAI}
                    disabled={!processingContext.trim()}
                  >
                    <Text style={styles.contextButtonTextPrimary}>معالجة بالذكاء الاصطناعي</Text>
                  </TouchableOpacity>
                </View>
              </Card>
            )}

            {/* File Info Display */}
            {selectedFile && (
              <Card style={styles.fileInfoCard}>
                <View style={styles.fileInfo}>
                  <MaterialIcons
                    name={selectedFile.type === 'pdf' ? 'description' : 'image'}
                    size={24}
                    color={COLORS.primary}
                  />
                  <View style={styles.fileDetails}>
                    <Text style={styles.fileName}>{selectedFile.name}</Text>
                    <Text style={styles.fileSize}>
                      {selectedFile.size ? `${(selectedFile.size / 1024 / 1024).toFixed(2)} MB` : 'حجم غير معروف'}
                    </Text>
                  </View>
                  <TouchableOpacity onPress={() => setShowPreview(!showPreview)}>
                    <MaterialIcons name="visibility" size={24} color={COLORS.primary} />
                  </TouchableOpacity>
                </View>
              </Card>
            )}

            {/* Content Preview */}
            {showPreview && extractedContent && (
              <Card style={styles.previewCard}>
                <Text style={styles.previewTitle}>معاينة المحتوى</Text>
                <Text style={styles.previewText} numberOfLines={5}>
                  {extractedContent.substring(0, 300)}...
                </Text>
                {contentAnalysis && (
                  <View style={styles.analysisContainer}>
                    <Text style={styles.analysisTitle}>تحليل المحتوى:</Text>
                    <Text style={styles.analysisText}>
                      📊 عدد الكلمات: {contentAnalysis.wordCount}
                    </Text>
                    <Text style={styles.analysisText}>
                      ⏱️ وقت القراءة المقدر: {contentAnalysis.estimatedReadingTime} دقيقة
                    </Text>
                    <Text style={styles.analysisText}>
                      🌐 اللغة المكتشفة: {contentAnalysis.language === 'ar' ? 'العربية' : contentAnalysis.language === 'en' ? 'الإنجليزية' : 'مختلطة'}
                    </Text>
                  </View>
                )}
              </Card>
            )}
          </Animated.View>

          {/* Enhanced Form Fields */}
          <Animated.View style={[styles.formContainer, { opacity: fadeAnim }]}>
            {/* Subject Field */}
            <View style={styles.fieldContainer}>
              <Text style={styles.sectionTitle}>📚 معلومات الاختبار</Text>
              <View style={styles.inputContainer}>
                <Text style={styles.fieldLabel}>المادة أو الموضوع *</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="مثال: الرياضيات، التاريخ، الفيزياء..."
                  placeholderTextColor={COLORS.textTertiary}
                  value={subject}
                  onChangeText={setSubject}
                />
              </View>
            </View>

            {/* Question Count and Timer Row */}
            <View style={styles.rowContainer}>
              <View style={styles.halfField}>
                <Text style={styles.fieldLabel}>عدد الأسئلة</Text>
                <TextInput
                  style={styles.numberInput}
                  placeholder="10"
                  placeholderTextColor={COLORS.textTertiary}
                  value={questionCount}
                  onChangeText={setQuestionCount}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.halfField}>
                <Text style={styles.fieldLabel}>الوقت (دقيقة)</Text>
                <TextInput
                  style={styles.numberInput}
                  placeholder="15"
                  placeholderTextColor={COLORS.textTertiary}
                  value={timerMinutes}
                  onChangeText={setTimerMinutes}
                  keyboardType="numeric"
                />
                {estimatedTime > 0 && (
                  <Text style={styles.estimatedTime}>
                    الوقت المقترح: {estimatedTime} دقيقة
                  </Text>
                )}
              </View>
            </View>

            {/* Content Scope - Expandable Section */}
            <View style={styles.fieldContainer}>
              <Text style={styles.sectionTitle}>📖 نطاق المحتوى</Text>
              <TouchableOpacity
                style={[styles.expandableButton, showContentScope && styles.expandableButtonActive]}
                onPress={() => setShowContentScope(!showContentScope)}
              >
                <Text style={styles.expandableButtonText}>
                  {contentTypes.find(type => type.value === contentType)?.label || 'اختر نطاق المحتوى'}
                </Text>
                <MaterialIcons
                  name={showContentScope ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                  size={24}
                  color={COLORS.textSecondary}
                />
              </TouchableOpacity>

              {showContentScope && (
                <Animated.View style={styles.expandableContent}>
                  <View style={styles.contentTypeContainer}>
                    {contentTypes.map((type) => (
                      <TouchableOpacity
                        key={type.value}
                        style={[
                          styles.contentTypeChip,
                          contentType === type.value && styles.contentTypeChipActive
                        ]}
                        onPress={() => {
                          setContentType(type.value);
                          setShowContentScope(false);
                        }}
                      >
                        <Text style={[
                          styles.contentTypeText,
                          contentType === type.value && styles.contentTypeTextActive
                        ]}>
                          {type.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </Animated.View>
              )}
            </View>

            {/* Enhanced Question Types - Grid Layout */}
            <View style={styles.fieldContainer}>
              <Text style={styles.sectionTitle}>❓ نوع الأسئلة</Text>
              <View style={styles.questionTypeGrid}>
                {questionTypes.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.questionTypeGridItem,
                      questionType === type.value && styles.questionTypeGridItemActive
                    ]}
                    onPress={() => setQuestionType(type.value)}
                  >
                    <MaterialIcons
                      name={type.icon}
                      size={20}
                      color={questionType === type.value ? COLORS.textPrimary : COLORS.textSecondary}
                    />
                    <Text style={[
                      styles.questionTypeGridText,
                      questionType === type.value && styles.questionTypeGridTextActive
                    ]}>
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Language Selection - Expandable Section */}
            <View style={styles.fieldContainer}>
              <Text style={styles.sectionTitle}>🌐 لغة الأسئلة</Text>
              <TouchableOpacity
                style={[styles.expandableButton, showLanguageOptions && styles.expandableButtonActive]}
                onPress={() => setShowLanguageOptions(!showLanguageOptions)}
              >
                <Text style={styles.expandableButtonText}>
                  {questionLanguages.find(lang => lang.value === questionLanguage)?.label || 'اختر لغة الأسئلة'}
                </Text>
                <MaterialIcons
                  name={showLanguageOptions ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                  size={24}
                  color={COLORS.textSecondary}
                />
              </TouchableOpacity>

              {showLanguageOptions && (
                <Animated.View style={styles.expandableContent}>
                  <View style={styles.languageContainer}>
                    {questionLanguages.map((lang) => (
                      <TouchableOpacity
                        key={lang.value}
                        style={[
                          styles.languageOption,
                          questionLanguage === lang.value && styles.languageOptionActive
                        ]}
                        onPress={() => {
                          setQuestionLanguage(lang.value);
                          setShowLanguageOptions(false);
                        }}
                      >
                        <MaterialIcons
                          name={lang.icon}
                          size={20}
                          color={questionLanguage === lang.value ? COLORS.textPrimary : COLORS.textSecondary}
                        />
                        <Text style={[
                          styles.languageOptionText,
                          questionLanguage === lang.value && styles.languageOptionTextActive
                        ]}>
                          {lang.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </Animated.View>
              )}

              {detectedLanguage && (
                <Text style={styles.detectedLanguage}>
                  🔍 اللغة المكتشفة: {detectedLanguage === 'ar' ? 'العربية' : detectedLanguage === 'en' ? 'الإنجليزية' : 'مختلطة'}
                </Text>
              )}
            </View>

            {/* Enhanced Special Notes */}
            <View style={styles.fieldContainer}>
              <Text style={styles.sectionTitle}>📝 ملاحظات خاصة (اختياري)</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                placeholder="مثال: ركز على الفصل الثالث، أضف أسئلة تطبيقية، تجنب الأسئلة النظرية..."
                placeholderTextColor={COLORS.textTertiary}
                value={specialNotes}
                onChangeText={setSpecialNotes}
                multiline
                numberOfLines={4}
              />
            </View>

            {/* Save Template Option */}
            {subject && questionType && (
              <TouchableOpacity style={styles.saveTemplateButton} onPress={saveTemplate}>
                <MaterialIcons name="bookmark-add" size={20} color={COLORS.primary} />
                <Text style={styles.saveTemplateText}>حفظ كقالب</Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        </ScrollView>

        {/* Enhanced Bottom Section */}
        <Animated.View style={[styles.bottomContainer, { opacity: fadeAnim }]}>
          {/* Progress Indicator */}
          {loading && (
            <View style={styles.progressContainer}>
              <ProgressBar progress={progress} color={COLORS.primary} style={styles.progressBar} />
              <Text style={styles.progressText}>{processingStep}</Text>
            </View>
          )}

          {/* Quiz Info Summary */}
          {selectedFile && subject && !loading && (
            <View style={styles.summaryContainer}>
              <Text style={styles.summaryText}>
                📁 {selectedFile.name} • 📚 {subject} •
                {questionCount ? ` ${questionCount} سؤال` : ' توليد ذكي'} •
                ⏱️ {timerMinutes} دقيقة
              </Text>
              {estimatedTime > 0 && (
                <Text style={styles.estimatedTimeText}>
                  الوقت المقدر للإكمال: {estimatedTime} دقيقة
                </Text>
              )}
            </View>
          )}

          {/* Enhanced Start Button */}
          <TouchableOpacity
            style={[
              styles.startButton,
              (!selectedFile || !subject || loading) && styles.startButtonDisabled
            ]}
            onPress={handleStartQuiz}
            disabled={!selectedFile || !subject || loading}
          >
            <MaterialIcons
              name={loading ? "hourglass-empty" : "play-arrow"}
              size={24}
              color={COLORS.textPrimary}
            />
            <Text style={styles.startButtonText}>
              {loading ? 'جاري المعالجة...' : 'إنشاء الاختبار'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Templates Modal */}
        {showTemplates && (
          <View style={styles.templatesModal}>
            <Card style={styles.templatesCard}>
              <Text style={styles.templatesTitle}>القوالب المحفوظة</Text>
              {savedTemplates.length === 0 ? (
                <Text style={styles.noTemplatesText}>لا توجد قوالب محفوظة</Text>
              ) : (
                savedTemplates.map((template) => (
                  <TouchableOpacity
                    key={template.id}
                    style={styles.templateItem}
                    onPress={() => loadTemplate(template)}
                  >
                    <Text style={styles.templateName}>{template.name}</Text>
                    <Text style={styles.templateDetails}>
                      {template.questionType} • {template.questionCount} سؤال
                    </Text>
                  </TouchableOpacity>
                ))
              )}
              <TouchableOpacity
                style={styles.closeTemplatesButton}
                onPress={() => setShowTemplates(false)}
              >
                <Text style={styles.closeTemplatesText}>إغلاق</Text>
              </TouchableOpacity>
            </Card>
          </View>
        )}

        {/* Sources Selection Modal */}
        <SourcesModal
          visible={showSourcesModal}
          onClose={() => setShowSourcesModal(false)}
          onSelectSource={handleSourceSelection}
          title="اختيار ملف للاختبار"
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.small,
  },
  headerTitle: {
    flex: 1,
    ...TYPOGRAPHY.headlineMedium,
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  templateButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  titleContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 140,
    gap: 24,
  },
  // Upload Section Styles
  uploadSection: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 12,
  },
  uploadButtonsContainer: {
    gap: 12,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.cardBackground,
    padding: 16,
    borderRadius: 12,
    gap: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  uploadButtonSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.cardBackgroundHover,
  },
  uploadIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.iconBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadTextContainer: {
    flex: 1,
  },
  uploadText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    textAlign: 'right',
    marginBottom: 4,
  },
  uploadSubtext: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
  // File Info Styles
  fileInfoCard: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fileDetails: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    textAlign: 'right',
  },
  fileSize: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
  // Preview Styles
  previewCard: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 8,
    textAlign: 'right',
  },
  previewText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 20,
    textAlign: 'right',
  },
  analysisContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  analysisTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 8,
    textAlign: 'right',
  },
  analysisText: {
    fontSize: 13,
    color: COLORS.textSecondary,
    marginBottom: 4,
    textAlign: 'right',
  },
  // Form Styles
  formContainer: {
    gap: 24,
  },
  fieldContainer: {
    gap: 12,
  },
  inputContainer: {
    gap: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
  textInput: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: COLORS.textPrimary,
    textAlign: 'right',
    height: 56,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  // Row Layout Styles
  rowContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  halfField: {
    flex: 1,
    gap: 8,
  },
  numberInput: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: COLORS.textPrimary,
    textAlign: 'center',
    height: 56,
    borderWidth: 1,
    borderColor: 'transparent',
  },

  estimatedTime: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  // Question Type Grid Styles
  questionTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'space-between',
  },
  questionTypeGridItem: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    width: '48%', // Two items per row
    minHeight: 80,
  },
  questionTypeGridItemActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primaryHover,
  },
  questionTypeGridText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  questionTypeGridTextActive: {
    color: COLORS.textPrimary,
    fontWeight: 'bold',
  },
  // Expandable Button Styles
  expandableButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  expandableButtonActive: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.cardBackgroundHover,
  },
  expandableButtonText: {
    fontSize: 16,
    color: COLORS.textPrimary,
    fontWeight: '500',
    textAlign: 'right',
  },
  expandableContent: {
    marginTop: 12,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 12,
  },
  // Content Type Styles
  contentTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  contentTypeChip: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  contentTypeChipActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primaryHover,
  },
  contentTypeText: {
    fontSize: 14,
    color: COLORS.textPrimary,
    fontWeight: '500',
  },
  contentTypeTextActive: {
    color: COLORS.textPrimary,
    fontWeight: 'bold',
  },
  // Language Selection Styles
  languageContainer: {
    gap: 8,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 8,
    padding: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  languageOptionActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primaryHover,
  },
  languageOptionText: {
    fontSize: 14,
    color: COLORS.textPrimary,
    fontWeight: '500',
    textAlign: 'right',
  },
  languageOptionTextActive: {
    color: COLORS.textPrimary,
    fontWeight: 'bold',
  },
  detectedLanguage: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'right',
    marginTop: 8,
    fontStyle: 'italic',
  },
  // Template Styles
  saveTemplateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 12,
    gap: 8,
    marginTop: 8,
  },
  saveTemplateText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '600',
  },
  // Bottom Section Styles
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.background,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    gap: 12,
  },
  progressContainer: {
    gap: 8,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  summaryContainer: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 12,
    gap: 4,
  },
  summaryText: {
    fontSize: 14,
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  estimatedTimeText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  startButton: {
    flexDirection: 'row',
    backgroundColor: COLORS.primary,
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  startButtonDisabled: {
    backgroundColor: COLORS.iconBackground,
    opacity: 0.6,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  // Templates Modal Styles
  templatesModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  templatesCard: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxHeight: '80%',
  },
  templatesTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: 16,
  },
  noTemplatesText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    padding: 20,
  },
  templateItem: {
    backgroundColor: COLORS.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    textAlign: 'right',
    marginBottom: 4,
  },
  templateDetails: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
  closeTemplatesButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    padding: 12,
    marginTop: 16,
  },
  closeTemplatesText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },

  // New styles for delayed processing workflow
  contextCard: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  contextDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  contextInput: {
    backgroundColor: COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    padding: 12,
    fontSize: 16,
    color: COLORS.textPrimary,
    minHeight: 100,
    marginBottom: 16,
  },
  contextActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  contextButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.background,
    alignItems: 'center',
  },
  contextButtonPrimary: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  contextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  contextButtonTextPrimary: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  fileStatus: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 4,
  },
  sourceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressSection: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
});

export default QuizSetupScreen;
