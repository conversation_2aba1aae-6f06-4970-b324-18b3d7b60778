import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
  Dimensions,
  ActivityIndicator,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import fileProcessingService from '../services/fileProcessingService';
import sourcesRegistry from '../services/sourcesRegistry';

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';
import UnifiedHeader from '../components/UnifiedHeader';
import UnifiedButton from '../components/UnifiedButton';
import UnifiedCard from '../components/UnifiedCard';
import SourcesModal from '../components/SourcesModal';

const { width: screenWidth } = Dimensions.get('window');

const ChatbotScreen = ({ route, navigation }) => {
  const { question } = route.params || {};
  // Enhanced state management
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: `مرحباً! أنا مساعدك الذكي لإنشاء الاختبارات وتحليل المحتوى التعليمي. يمكنني مساعدتك في:

• إنشاء أسئلة متنوعة من النصوص
• تحليل المحتوى التعليمي
• اقتراح أنواع أسئلة مختلفة
• رفع ملفات PDF والمستندات

كيف يمكنني مساعدتك اليوم؟ يمكنك كتابة سؤالك أو استخدام زر المرفقات لرفع ملف.`,
      isUser: false,
      timestamp: new Date(),
      type: 'welcome'
    }
  ]);

  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [chatHistoryList, setChatHistoryList] = useState([]);
  const [apiKey, setApiKey] = useState('');

  // New state for delayed processing workflow
  const [availableSources, setAvailableSources] = useState([]);
  const [showSourcesModal, setShowSourcesModal] = useState(false);
  const [selectedSource, setSelectedSource] = useState(null);
  const [processingContext, setProcessingContext] = useState('');

  const scrollViewRef = useRef(null);
  const typingAnimation = useRef(new Animated.Value(0)).current;

  // Initialize data on component mount only
  useEffect(() => {
    const initializeData = async () => {
      await loadConversationHistory();
      await loadApiKey();
      await loadChatHistory();
    };
    initializeData();
  }, []); // Empty dependency array - run only once on mount

  // Auto scroll when messages change
  useEffect(() => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  }, [messages]);

  // Save conversation history when messages change (but not on initial load)
  useEffect(() => {
    if (messages.length > 1) { // Only save if there are actual messages beyond welcome
      saveConversationHistory();
    }
  }, [messages]);

  // Load conversation history from AsyncStorage (only on app start)
  const loadConversationHistory = async () => {
    try {
      const savedMessages = await AsyncStorage.getItem('chatbot_messages');
      if (savedMessages) {
        const parsedMessages = JSON.parse(savedMessages);
        if (parsedMessages && parsedMessages.length > 0) {
          // Only load if we don't already have messages (prevent overwriting current conversation)
          if (messages.length <= 1) {
            setMessages(parsedMessages);
          }
        }
      }
    } catch (error) {
      console.log('Error loading conversation history:', error);
    }
  };

  // Save conversation history to AsyncStorage
  const saveConversationHistory = async () => {
    try {
      await AsyncStorage.setItem('chatbot_messages', JSON.stringify(messages));
    } catch (error) {
      console.log('Error saving conversation history:', error);
    }
  };

  // Load API key from AsyncStorage
  const loadApiKey = async () => {
    try {
      const savedApiKey = await AsyncStorage.getItem('chatbot_api_key');
      if (savedApiKey) {
        setApiKey(savedApiKey);
      }
    } catch (error) {
      console.log('Error loading API key:', error);
    }
  };

  // Save API key to AsyncStorage
  const saveApiKey = async (key) => {
    try {
      await AsyncStorage.setItem('chatbot_api_key', key);
      setApiKey(key);
    } catch (error) {
      console.log('Error saving API key:', error);
    }
  };

  // Enhanced sendMessage with AI integration
  const sendMessage = async (messageText = inputText, messageType = 'text') => {
    if (!messageText.trim() && messageType === 'text') return;

    const userMessage = {
      id: Date.now(),
      text: messageText.trim(),
      isUser: true,
      timestamp: new Date(),
      type: messageType,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      // Simulate AI processing with enhanced responses
      await new Promise(resolve => setTimeout(resolve, 2000));

      let botResponse = '';

      // Intelligent response generation based on message content
      if (messageText.includes('اختبار') || messageText.includes('أسئلة')) {
        botResponse = `ممتاز! يمكنني مساعدتك في إنشاء اختبار شامل. سأحتاج إلى:

• المحتوى أو النص المراد إنشاء أسئلة منه
• نوع الأسئلة المطلوبة (اختيار متعدد، صح/خطأ، إجابة قصيرة)
• مستوى الصعوبة المطلوب
• عدد الأسئلة المرغوب

يرجى مشاركة المحتوى أو رفع ملف لبدء إنشاء الاختبار.`;
      } else if (messageText.includes('ملف') || messageText.includes('رفع')) {
        botResponse = `بالطبع! يمكنني تحليل الملفات التالية:

• ملفات PDF
• مستندات Word
• ملفات نصية
• صور تحتوي على نص

استخدم زر المرفقات (📎) في أسفل الشاشة لاختيار الملف المطلوب.`;
      } else {
        botResponse = `شكراً لك على رسالتك. كيف يمكنني مساعدتك في إنشاء اختبارات أو تحليل المحتوى التعليمي؟

يمكنني:
• إنشاء أسئلة متنوعة من النصوص
• تحليل المستندات والملفات
• اقتراح استراتيجيات تقييم
• مساعدتك في تطوير اختبارات شاملة

يرجى مشاركة المحتوى أو طرح سؤال محدد.`;
      }

      const aiMessage = {
        id: Date.now() + 1,
        text: botResponse,
        isUser: false,
        timestamp: new Date(),
        type: 'ai_response',
      };

      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        text: 'عذراً، حدث خطأ في معالجة رسالتك. يرجى المحاولة مرة أخرى.',
        isUser: false,
        timestamp: new Date(),
        type: 'error',
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  // Enhanced file upload with delayed processing
  const handleFileUpload = async () => {
    try {
      setIsLoading(true);

      // Phase 1: Upload and cache file without AI processing
      const result = await fileProcessingService.uploadAndCacheFile();

      if (!result.success) {
        Alert.alert('خطأ', result.error);
        return;
      }

      // Add file to uploaded files list
      setUploadedFiles(prev => [...prev, result.file]);

      // Create upload message
      const uploadMessage = {
        id: Date.now(),
        text: `📁 تم رفع الملف: ${result.file.name}`,
        isUser: true,
        timestamp: new Date(),
        type: 'file_upload',
        file: result.file,
      };

      setMessages(prev => [...prev, uploadMessage]);

      // Create ready message
      const readyMessage = {
        id: Date.now() + 1,
        text: result.isDuplicate
          ? `✅ تم العثور على الملف في الذاكرة المؤقتة!\n\nيمكنك الآن طرح أسئلة حول المحتوى أو طلب معالجة خاصة.`
          : `✅ تم رفع الملف وحفظه بنجاح!\n\nيمكنك الآن:\n• طرح أسئلة حول المحتوى\n• طلب إنشاء أسئلة اختبار\n• طلب ملخص أو تحليل\n• أي معالجة أخرى تحتاجها`,
        isUser: false,
        timestamp: new Date(),
        type: 'file_ready',
      };

      setMessages(prev => [...prev, readyMessage]);

      // Record usage in sources registry
      await sourcesRegistry.recordSourceUsage(result.file.hash, 'ChatBot');

    } catch (error) {
      console.error('Error uploading file:', error);
      Alert.alert('خطأ', 'فشل في رفع الملف');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle file selection from sources
  const handleSelectFromSources = async () => {
    try {
      setIsLoading(true);
      const sources = await sourcesRegistry.getRecentSources(20);
      setAvailableSources(sources);
      setShowSourcesModal(true);
    } catch (error) {
      console.error('Error loading sources:', error);
      Alert.alert('خطأ', 'فشل في تحميل المصادر المحفوظة');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle source selection
  const handleSourceSelection = (source) => {
    setSelectedSource(source);
    setShowSourcesModal(false);

    // Add source to uploaded files
    setUploadedFiles(prev => [...prev, source]);

    // Create selection message
    const selectionMessage = {
      id: Date.now(),
      text: `📂 تم اختيار الملف: ${source.filename}`,
      isUser: true,
      timestamp: new Date(),
      type: 'source_selected',
      file: source,
    };

    setMessages(prev => [...prev, selectionMessage]);

    // Create ready message
    const readyMessage = {
      id: Date.now() + 1,
      text: `✅ الملف جاهز للاستخدام!\n\n${source.status === 'processed' ? 'هذا الملف معالج مسبقاً بالذكاء الاصطناعي.' : 'يمكنني معالجة هذا الملف حسب طلبك.'}\n\nما الذي تريد أن أفعله بالمحتوى؟`,
      isUser: false,
      timestamp: new Date(),
      type: 'source_ready',
    };

    setMessages(prev => [...prev, readyMessage]);

    // Record usage
    sourcesRegistry.recordSourceUsage(source.hash, 'ChatBot');
  };

  // Chat history management
  const loadChatHistory = async () => {
    try {
      const savedHistory = await AsyncStorage.getItem('chat_history_list');
      if (savedHistory) {
        setChatHistoryList(JSON.parse(savedHistory));
      }
    } catch (error) {
      console.log('Error loading chat history:', error);
    }
  };

  const saveChatToHistory = async (messagesToSave = messages) => {
    try {
      // Only save if there are actual conversation messages (more than just welcome message)
      if (messagesToSave.length <= 1) {
        return;
      }

      // Find the first user message to use as title
      const firstUserMessage = messagesToSave.find(msg => msg.isUser);
      const title = firstUserMessage
        ? (firstUserMessage.text.length > 50
           ? firstUserMessage.text.substring(0, 50) + '...'
           : firstUserMessage.text)
        : 'محادثة جديدة';

      const chatSession = {
        id: Date.now().toString(),
        title: title,
        messages: [...messagesToSave], // Create a copy to avoid reference issues
        timestamp: new Date().toISOString(), // Use ISO string for better serialization
        messageCount: messagesToSave.length - 1, // Exclude welcome message
      };

      // Load current history to ensure we have the latest data
      const currentHistory = await AsyncStorage.getItem('chat_history_list');
      const historyList = currentHistory ? JSON.parse(currentHistory) : [];

      const updatedHistory = [chatSession, ...historyList.slice(0, 19)]; // Keep last 20 chats
      setChatHistoryList(updatedHistory);
      await AsyncStorage.setItem('chat_history_list', JSON.stringify(updatedHistory));
    } catch (error) {
      console.log('Error saving chat to history:', error);
    }
  };

  const deleteChatFromHistory = async (chatId) => {
    Alert.alert(
      'حذف المحادثة',
      'هل أنت متأكد من حذف هذه المحادثة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              // Load current history to ensure we have the latest data
              const currentHistory = await AsyncStorage.getItem('chat_history_list');
              const historyList = currentHistory ? JSON.parse(currentHistory) : [];

              const updatedHistory = historyList.filter(chat => chat.id !== chatId);
              setChatHistoryList(updatedHistory);
              await AsyncStorage.setItem('chat_history_list', JSON.stringify(updatedHistory));
            } catch (error) {
              console.log('Error deleting chat from history:', error);
            }
          },
        },
      ]
    );
  };

  const loadChatFromHistory = async (chat) => {
    try {
      // Save current conversation before loading new one
      if (messages.length > 1) {
        await saveChatToHistory();
      }

      // Load the selected chat
      setMessages([...chat.messages]); // Create a copy to avoid reference issues
      setShowChatHistory(false);

      // Clear the current conversation from AsyncStorage and save the loaded one
      await AsyncStorage.setItem('chatbot_messages', JSON.stringify(chat.messages));
    } catch (error) {
      console.log('Error loading chat from history:', error);
    }
  };

  const startNewChat = async () => {
    try {
      // Save current conversation to history if it has messages
      if (messages.length > 1) {
        await saveChatToHistory();
      }

      // Reset to welcome message only
      const welcomeMessage = messages[0];
      setMessages([welcomeMessage]);
      setShowChatHistory(false);

      // Clear current conversation from AsyncStorage
      await AsyncStorage.setItem('chatbot_messages', JSON.stringify([welcomeMessage]));
    } catch (error) {
      console.log('Error starting new chat:', error);
    }
  };

  // Clear conversation
  const clearConversation = () => {
    Alert.alert(
      'مسح المحادثة',
      'هل أنت متأكد من مسح جميع الرسائل؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'مسح',
          style: 'destructive',
          onPress: async () => {
            try {
              // Save current conversation to history before clearing
              if (messages.length > 1) {
                await saveChatToHistory();
              }

              // Reset to welcome message only
              const welcomeMessage = messages[0];
              setMessages([welcomeMessage]);

              // Clear current conversation from AsyncStorage
              await AsyncStorage.setItem('chatbot_messages', JSON.stringify([welcomeMessage]));
            } catch (error) {
              console.log('Error clearing conversation:', error);
            }
          },
        },
      ]
    );
  };

  // Delete current conversation from history
  const deleteCurrentConversation = async () => {
    try {
      // Save current conversation to history first
      if (messages.length > 1) {
        await saveChatToHistory();

        // Get the latest history to find the current conversation
        const currentHistory = await AsyncStorage.getItem('chat_history_list');
        const historyList = currentHistory ? JSON.parse(currentHistory) : [];

        if (historyList.length > 0) {
          // Remove the most recent conversation (which is the current one we just saved)
          const updatedHistory = historyList.slice(1);
          setChatHistoryList(updatedHistory);
          await AsyncStorage.setItem('chat_history_list', JSON.stringify(updatedHistory));
        }
      }

      // Reset to welcome message
      const welcomeMessage = messages[0];
      setMessages([welcomeMessage]);
      await AsyncStorage.setItem('chatbot_messages', JSON.stringify([welcomeMessage]));
    } catch (error) {
      console.log('Error deleting current conversation:', error);
    }
  };

  // Enhanced renderMessage with new features
  // Enhanced renderMessage with new design and features
  const renderMessage = (message) => {
    return (
      <View
        key={message.id}
        style={[
          styles.messageContainer,
          message.isUser ? styles.userMessageContainer : styles.aiMessageContainer
        ]}
      >
        {!message.isUser && (
          <View style={styles.aiAvatar}>
            <MaterialIcons name="smart-toy" size={24} color={COLORS.primary} />
          </View>
        )}

        <View style={styles.messageContent}>
          <UnifiedCard
            variant={message.isUser ? "elevated" : "default"}
            padding="medium"
            style={[
              styles.messageBubble,
              message.isUser ? styles.userBubble : styles.aiBubble
            ]}
          >
            {/* Message header with sender and timestamp */}
            <View style={styles.messageHeader}>
              <Text style={[
                styles.senderName,
                { color: message.isUser ? COLORS.textPrimary : COLORS.textSecondary }
              ]}>
                {message.isUser ? 'أنت' : 'المساعد الذكي'}
              </Text>
              <Text style={styles.timestamp}>
                {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </Text>
            </View>

            {/* Message content */}
            <Text style={[
              styles.messageText,
              { color: COLORS.textPrimary }
            ]}>
              {message.text}
            </Text>

            {/* File attachment indicator */}
            {message.type === 'file_upload' && message.file && (
              <View style={styles.fileAttachment}>
                <MaterialIcons name="attach-file" size={16} color={COLORS.textSecondary} />
                <Text style={styles.fileName}>{message.file.name}</Text>
              </View>
            )}



            {/* Message status indicator */}
            {message.isUser && (
              <View style={styles.messageStatus}>
                <MaterialIcons
                  name="done"
                  size={14}
                  color={COLORS.success}
                />
              </View>
            )}
          </UnifiedCard>
        </View>

        {message.isUser && (
          <View style={styles.userAvatar}>
            <MaterialIcons name="person" size={24} color={COLORS.textSecondary} />
          </View>
        )}
      </View>
    );
  };

  // Render chat history modal
  const renderChatHistory = () => {
    if (!showChatHistory) return null;

    return (
      <View style={styles.historyModal}>
        <View style={styles.historyContainer}>
          <View style={styles.historyHeader}>
            <Text style={styles.historyTitle}>سجل المحادثات</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowChatHistory(false)}
            >
              <MaterialIcons name="close" size={24} color={COLORS.textPrimary} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.newChatButton}
            onPress={startNewChat}
          >
            <MaterialIcons name="add" size={20} color={COLORS.primary} />
            <Text style={styles.newChatText}>محادثة جديدة</Text>
          </TouchableOpacity>

          <ScrollView style={styles.historyList}>
            {chatHistoryList.length === 0 ? (
              <Text style={styles.emptyHistoryText}>لا توجد محادثات سابقة</Text>
            ) : (
              chatHistoryList.map((chat) => (
                <View key={chat.id} style={styles.historyItem}>
                  <TouchableOpacity
                    style={styles.historyItemContent}
                    onPress={() => loadChatFromHistory(chat)}
                  >
                    <Text style={styles.historyItemTitle} numberOfLines={2}>
                      {chat.title}
                    </Text>
                    <Text style={styles.historyItemInfo}>
                      {chat.messageCount} رسالة • {new Date(chat.timestamp).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => deleteChatFromHistory(chat.id)}
                  >
                    <MaterialIcons name="delete-outline" size={20} color={COLORS.error} />
                  </TouchableOpacity>
                </View>
              ))
            )}
          </ScrollView>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      <SafeAreaView style={styles.safeArea}>
        {/* Enhanced Header */}
        <UnifiedHeader
          title="المساعد الذكي"
          subtitle="إنشاء الاختبارات وتحليل المحتوى"
          showBackButton={true}
          onBackPress={() => navigation.goBack()}
          rightComponent={
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.headerActionButton}
                onPress={clearConversation}
              >
                <MaterialIcons name="delete-outline" size={24} color={COLORS.textSecondary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerActionButton}
                onPress={() => setShowChatHistory(!showChatHistory)}
              >
                <MaterialIcons name="history" size={24} color={COLORS.textSecondary} />
              </TouchableOpacity>
            </View>
          }
        />

        {/* Chat History Modal */}
        {renderChatHistory()}

        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Messages */}
          <ScrollView
            ref={scrollViewRef}
            style={styles.messagesContainer}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
          >
            {messages.map(renderMessage)}

            {/* Typing Indicator */}
            {isTyping && (
              <View style={styles.typingIndicator}>
                <View style={styles.aiAvatar}>
                  <MaterialIcons name="smart-toy" size={24} color={COLORS.primary} />
                </View>
                <View style={styles.typingBubble}>
                  <Text style={styles.typingText}>المساعد يكتب...</Text>
                  <View style={styles.typingDots}>
                    <Animated.View style={[styles.typingDot, { opacity: typingAnimation }]} />
                    <Animated.View style={[styles.typingDot, { opacity: typingAnimation }]} />
                    <Animated.View style={[styles.typingDot, { opacity: typingAnimation }]} />
                  </View>
                </View>
              </View>
            )}
          </ScrollView>

          {/* Enhanced Input Area */}
          <View style={styles.inputContainer}>
            <UnifiedCard variant="elevated" padding="small" style={styles.inputCard}>
              <View style={styles.inputWrapper}>
                <TouchableOpacity
                  style={styles.attachButton}
                  onPress={handleFileUpload}
                >
                  <MaterialIcons name="attach-file" size={24} color={COLORS.textSecondary} />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.attachButton}
                  onPress={handleSelectFromSources}
                >
                  <MaterialIcons name="folder" size={24} color={COLORS.textSecondary} />
                </TouchableOpacity>

                <TextInput
                  style={styles.textInput}
                  placeholder="اكتب رسالتك هنا..."
                  placeholderTextColor={COLORS.textSecondary}
                  value={inputText}
                  onChangeText={setInputText}
                  multiline
                  maxLength={1000}
                  textAlign="right"
                />

                <TouchableOpacity
                  style={[
                    styles.sendButton,
                    {
                      backgroundColor: inputText.trim() ? COLORS.primary : COLORS.cardBackground,
                    }
                  ]}
                  onPress={() => sendMessage()}
                  disabled={!inputText.trim() || isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color={COLORS.textPrimary} />
                  ) : (
                    <MaterialIcons
                      name="send"
                      size={20}
                      color={inputText.trim() ? COLORS.textPrimary : COLORS.textSecondary}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </UnifiedCard>
          </View>
        </KeyboardAvoidingView>

        {/* Sources Selection Modal */}
        <SourcesModal
          visible={showSourcesModal}
          onClose={() => setShowSourcesModal(false)}
          onSelectSource={handleSourceSelection}
          title="اختيار ملف للمحادثة"
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.aiMessageBg,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },

  // Header Actions
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.cardBackground, // #1A102B
  },

  // Chat History Modal
  historyModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(14, 8, 26, 0.9)', // Semi-transparent background
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyContainer: {
    backgroundColor: COLORS.cardBackground, // #1A102B
    borderRadius: 16,
    width: screenWidth * 0.9,
    maxHeight: screenWidth * 1.2,
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textPrimary, // #FFFFFF
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background, // #0E081A
  },
  newChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
    padding: 12,
    backgroundColor: COLORS.background, // #0E081A
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.primary, // #6F2CFF
    gap: 8,
  },
  newChatText: {
    fontSize: 16,
    color: COLORS.primary, // #6F2CFF
    fontWeight: '500',
  },
  historyList: {
    maxHeight: 400,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.background,
  },
  historyItemContent: {
    flex: 1,
    marginRight: 12,
  },
  historyItemTitle: {
    fontSize: 16,
    color: COLORS.textPrimary, // #FFFFFF
    fontWeight: '500',
    marginBottom: 4,
  },
  historyItemInfo: {
    fontSize: 12,
    color: COLORS.textSecondary, // #A88FDB
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background, // #0E081A
  },
  emptyHistoryText: {
    textAlign: 'center',
    color: COLORS.textSecondary, // #A88FDB
    fontSize: 16,
    padding: 32,
  },
  keyboardAvoid: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    gap: 24,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 10,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.cardBackground, // #1A102B
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
  },
  userAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.cardBackground, // #1A102B
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
  },
  messageContent: {
    flex: 1,
    marginHorizontal: 8,
  },

  // Enhanced message bubble styles
  messageBubble: {
    borderRadius: 16,
    maxWidth: screenWidth * 0.8,
  },
  userBubble: {
    backgroundColor: COLORS.primary, // #6F2CFF
    alignSelf: 'flex-end',
  },
  aiBubble: {
    backgroundColor: COLORS.cardBackground, // #1A102B
    alignSelf: 'flex-start',
  },

  // Message header and content
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.textSecondary, // #A88FDB
  },
  timestamp: {
    fontSize: 10,
    color: COLORS.textSecondary, // #A88FDB
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    color: COLORS.textPrimary, // #FFFFFF
    textAlign: 'right',
  },

  // File attachment styles
  fileAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: COLORS.background, // #0E081A
    borderRadius: 8,
    gap: 8,
  },
  fileName: {
    fontSize: 14,
    color: COLORS.textSecondary, // #A88FDB
    flex: 1,
  },



  // Message status
  messageStatus: {
    position: 'absolute',
    bottom: 4,
    right: 8,
  },

  // Typing indicator
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  typingBubble: {
    backgroundColor: COLORS.cardBackground, // #1A102B
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
  },
  typingText: {
    fontSize: 14,
    color: COLORS.textSecondary, // #A88FDB
    fontStyle: 'italic',
  },
  typingDots: {
    flexDirection: 'row',
    marginTop: 4,
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.primary, // #6F2CFF
  },

  // Enhanced input area styles
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background, // #0E081A
    borderTopWidth: 1,
    borderTopColor: COLORS.border, // #A88FDB
  },
  inputCard: {
    backgroundColor: COLORS.cardBackground, // #1A102B
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  attachButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background, // #0E081A
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.textPrimary, // #FFFFFF
    backgroundColor: 'transparent',
    maxHeight: 120,
    minHeight: 40,
    textAlignVertical: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChatbotScreen;
