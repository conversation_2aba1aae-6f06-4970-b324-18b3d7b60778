import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  StatusBar,
  TextInput,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../theme/colors';
import AIService from '../services/aiService';

const SettingsScreen = ({ navigation }) => {
  const [apiKey, setApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isApiKeyConfigured, setIsApiKeyConfigured] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    loadApiKeyStatus();
  }, []);

  const loadApiKeyStatus = async () => {
    try {
      const configured = await AIService.isApiKeyConfigured();
      setIsApiKeyConfigured(configured);

      if (configured) {
        const storedKey = await AIService.getStoredApiKey();
        if (storedKey) {
          setApiKey('*'.repeat(storedKey.length - 4) + storedKey.slice(-4));
        }
      }
    } catch (error) {
      console.error('Error loading API key status:', error);
    }
  };

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال مفتاح API صحيح');
      return;
    }

    if (!apiKey.startsWith('AIza') || apiKey.length < 30) {
      Alert.alert('خطأ', 'تنسيق مفتاح Gemini API غير صحيح. يجب أن يبدأ بـ "AIza"');
      return;
    }

    setIsLoading(true);
    try {
      await AIService.setApiKey(apiKey);
      setIsApiKeyConfigured(true);
      Alert.alert('تم الحفظ', 'تم حفظ مفتاح API بنجاح. يمكنك الآن استخدام جميع ميزات التطبيق.');
    } catch (error) {
      console.error('Error saving API key:', error);
      Alert.alert('خطأ', 'فشل في حفظ مفتاح API. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  const clearAllData = () => {
    Alert.alert(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.clear();
              Alert.alert('تم الحذف', 'تم حذف جميع البيانات بنجاح');
              setApiKey('');
              setIsApiKeyConfigured(false);
            } catch (error) {
              Alert.alert('خطأ', 'فشل في حذف البيانات');
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.background} barStyle="light-content" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>الإعدادات</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>إعدادات الذكاء الاصطناعي</Text>

            <View style={styles.apiSection}>
              <View style={styles.apiHeader}>
                <MaterialIcons name="vpn-key" size={24} color={COLORS.primary} />
                <Text style={styles.apiTitle}>مفتاح Gemini API</Text>
              </View>

              <Text style={styles.apiDescription}>
                يتطلب التطبيق مفتاح Gemini API للعمل. يمكنك الحصول على مفتاح مجاني من Google AI Studio.
              </Text>

              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="أدخل مفتاح Gemini API"
                  placeholderTextColor={COLORS.textSecondary}
                  value={apiKey}
                  onChangeText={setApiKey}
                  secureTextEntry={!showApiKey}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowApiKey(!showApiKey)}
                >
                  <MaterialIcons
                    name={showApiKey ? "visibility-off" : "visibility"}
                    size={20}
                    color={COLORS.textSecondary}
                  />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveApiKey}
                disabled={isLoading}
              >
                <Text style={styles.saveButtonText}>
                  {isLoading ? "جاري الحفظ..." : "حفظ المفتاح"}
                </Text>
              </TouchableOpacity>

              <View style={styles.statusContainer}>
                <MaterialIcons
                  name={isApiKeyConfigured ? "check-circle" : "error"}
                  size={20}
                  color={isApiKeyConfigured ? COLORS.success : COLORS.error}
                />
                <Text style={[
                  styles.statusText,
                  { color: isApiKeyConfigured ? COLORS.success : COLORS.error }
                ]}>
                  {isApiKeyConfigured ? "مفتاح API مُكوَّن" : "مفتاح API غير مُكوَّن"}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>إدارة البيانات</Text>

            <TouchableOpacity style={styles.settingItem} onPress={clearAllData}>
              <View style={styles.settingLeft}>
                <View style={styles.iconContainer}>
                  <MaterialIcons name="delete-sweep" size={24} color={COLORS.primary} />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>حذف جميع البيانات</Text>
                  <Text style={styles.settingSubtitle}>حذف جميع الاختبارات والنتائج المحفوظة</Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={COLORS.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>حول التطبيق</Text>

            <TouchableOpacity style={styles.settingItem} onPress={() => {}}>
              <View style={styles.settingLeft}>
                <View style={styles.iconContainer}>
                  <MaterialIcons name="info" size={24} color={COLORS.primary} />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>معلومات التطبيق</Text>
                  <Text style={styles.settingSubtitle}>الإصدار 1.0.0</Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={COLORS.textSecondary} />
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.background,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  apiSection: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  apiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  apiTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginLeft: 12,
  },
  apiDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: 16,
  },
  input: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.textPrimary,
  },
  eyeButton: {
    padding: 12,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  saveButtonText: {
    color: COLORS.textPrimary,
    fontSize: 16,
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
});

export default SettingsScreen;