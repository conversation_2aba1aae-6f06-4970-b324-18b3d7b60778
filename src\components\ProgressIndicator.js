// Progress Indicator Component for TestMeApp
// Visual progress tracking and status indicators for file processing workflow

import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../theme/colors';

const ProgressIndicator = ({ 
  steps = [],
  currentStep = 0,
  showLabels = true,
  size = 'medium',
  variant = 'horizontal' // 'horizontal' or 'vertical'
}) => {
  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'active';
    return 'pending';
  };

  const getStepIcon = (step, status) => {
    if (status === 'completed') return 'check-circle';
    if (status === 'active') return step.activeIcon || 'radio-button-checked';
    return step.icon || 'radio-button-unchecked';
  };

  const getStepColor = (status) => {
    switch (status) {
      case 'completed':
        return COLORS.success;
      case 'active':
        return COLORS.primary;
      default:
        return COLORS.textSecondary;
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          iconSize: 20,
          fontSize: 12,
          spacing: 8,
          lineHeight: 2,
        };
      case 'large':
        return {
          iconSize: 32,
          fontSize: 16,
          spacing: 16,
          lineHeight: 4,
        };
      default: // medium
        return {
          iconSize: 24,
          fontSize: 14,
          spacing: 12,
          lineHeight: 3,
        };
    }
  };

  const sizeStyles = getSizeStyles();

  const StepItem = ({ step, index, status }) => (
    <View style={[
      styles.stepContainer,
      variant === 'vertical' && styles.stepContainerVertical,
      { marginRight: variant === 'horizontal' ? sizeStyles.spacing : 0 }
    ]}>
      <View style={[
        styles.stepIconContainer,
        { 
          width: sizeStyles.iconSize + 8,
          height: sizeStyles.iconSize + 8,
          borderRadius: (sizeStyles.iconSize + 8) / 2,
          backgroundColor: status === 'active' ? COLORS.primary + '20' : 'transparent'
        }
      ]}>
        <MaterialIcons 
          name={getStepIcon(step, status)} 
          size={sizeStyles.iconSize} 
          color={getStepColor(status)} 
        />
      </View>
      
      {showLabels && (
        <Text style={[
          styles.stepLabel,
          {
            fontSize: sizeStyles.fontSize,
            color: getStepColor(status),
            fontWeight: status === 'active' ? '600' : '400',
            marginTop: variant === 'horizontal' ? 4 : 0,
            marginLeft: variant === 'vertical' ? 8 : 0,
          }
        ]}>
          {step.label}
        </Text>
      )}
      
      {/* Connection Line */}
      {index < steps.length - 1 && (
        <View style={[
          styles.connectionLine,
          variant === 'vertical' ? styles.connectionLineVertical : styles.connectionLineHorizontal,
          {
            backgroundColor: index < currentStep ? COLORS.success : COLORS.border,
            [variant === 'horizontal' ? 'height' : 'width']: sizeStyles.lineHeight,
          }
        ]} />
      )}
    </View>
  );

  return (
    <View style={[
      styles.container,
      variant === 'vertical' && styles.containerVertical
    ]}>
      {steps.map((step, index) => (
        <StepItem
          key={index}
          step={step}
          index={index}
          status={getStepStatus(index)}
        />
      ))}
    </View>
  );
};

// Predefined step configurations for common workflows
export const FILE_PROCESSING_STEPS = [
  {
    label: 'اختيار الملف',
    icon: 'folder-open',
    activeIcon: 'folder',
  },
  {
    label: 'رفع الملف',
    icon: 'cloud-upload',
    activeIcon: 'cloud-upload',
  },
  {
    label: 'إدخال السياق',
    icon: 'edit',
    activeIcon: 'edit',
  },
  {
    label: 'معالجة بالذكاء الاصطناعي',
    icon: 'psychology',
    activeIcon: 'psychology',
  },
  {
    label: 'النتائج جاهزة',
    icon: 'check-circle',
    activeIcon: 'check-circle',
  },
];

export const QUIZ_SETUP_STEPS = [
  {
    label: 'إعداد الاختبار',
    icon: 'settings',
    activeIcon: 'settings',
  },
  {
    label: 'تحديد المحتوى',
    icon: 'description',
    activeIcon: 'description',
  },
  {
    label: 'إنشاء الأسئلة',
    icon: 'quiz',
    activeIcon: 'quiz',
  },
  {
    label: 'بدء الاختبار',
    icon: 'play-arrow',
    activeIcon: 'play-arrow',
  },
];

export const CHAT_WORKFLOW_STEPS = [
  {
    label: 'اختيار المصدر',
    icon: 'source',
    activeIcon: 'source',
  },
  {
    label: 'بدء المحادثة',
    icon: 'chat',
    activeIcon: 'chat',
  },
  {
    label: 'معالجة الطلب',
    icon: 'psychology',
    activeIcon: 'psychology',
  },
  {
    label: 'عرض النتائج',
    icon: 'visibility',
    activeIcon: 'visibility',
  },
];

// Animated Progress Bar Component
export const AnimatedProgressBar = ({ 
  progress = 0, // 0 to 1
  height = 4,
  backgroundColor = COLORS.border,
  progressColor = COLORS.primary,
  animated = true,
  showPercentage = false,
  style = {}
}) => {
  const animatedWidth = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (animated) {
      Animated.timing(animatedWidth, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      animatedWidth.setValue(progress);
    }
  }, [progress, animated]);

  return (
    <View style={[styles.progressBarContainer, style]}>
      <View style={[
        styles.progressBarBackground,
        { height, backgroundColor }
      ]}>
        <Animated.View style={[
          styles.progressBarFill,
          {
            height,
            backgroundColor: progressColor,
            width: animatedWidth.interpolate({
              inputRange: [0, 1],
              outputRange: ['0%', '100%'],
            }),
          }
        ]} />
      </View>
      
      {showPercentage && (
        <Text style={styles.percentageText}>
          {Math.round(progress * 100)}%
        </Text>
      )}
    </View>
  );
};

// Status Badge Component
export const StatusBadge = ({ 
  status, 
  label,
  size = 'medium',
  showIcon = true 
}) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'success':
      case 'completed':
        return {
          color: COLORS.success,
          icon: 'check-circle',
          backgroundColor: COLORS.success + '20',
        };
      case 'error':
      case 'failed':
        return {
          color: COLORS.error,
          icon: 'error',
          backgroundColor: COLORS.error + '20',
        };
      case 'warning':
        return {
          color: COLORS.warning || '#FFA500',
          icon: 'warning',
          backgroundColor: (COLORS.warning || '#FFA500') + '20',
        };
      case 'info':
      case 'processing':
        return {
          color: COLORS.primary,
          icon: 'info',
          backgroundColor: COLORS.primary + '20',
        };
      default:
        return {
          color: COLORS.textSecondary,
          icon: 'radio-button-unchecked',
          backgroundColor: COLORS.textSecondary + '20',
        };
    }
  };

  const config = getStatusConfig(status);
  const isSmall = size === 'small';

  return (
    <View style={[
      styles.statusBadge,
      { backgroundColor: config.backgroundColor },
      isSmall && styles.statusBadgeSmall
    ]}>
      {showIcon && (
        <MaterialIcons 
          name={config.icon} 
          size={isSmall ? 14 : 16} 
          color={config.color} 
        />
      )}
      <Text style={[
        styles.statusBadgeText,
        { 
          color: config.color,
          fontSize: isSmall ? 10 : 12,
          marginLeft: showIcon ? 4 : 0
        }
      ]}>
        {label}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  containerVertical: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  stepContainer: {
    alignItems: 'center',
    position: 'relative',
  },
  stepContainerVertical: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepLabel: {
    textAlign: 'center',
    maxWidth: 80,
  },
  connectionLine: {
    position: 'absolute',
  },
  connectionLineHorizontal: {
    top: '50%',
    right: -12,
    width: 24,
    transform: [{ translateY: -1.5 }],
  },
  connectionLineVertical: {
    left: 16,
    top: 32,
    height: 20,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBarBackground: {
    flex: 1,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    borderRadius: 2,
  },
  percentageText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 8,
    minWidth: 35,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeSmall: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  statusBadgeText: {
    fontWeight: '500',
  },
});

export default ProgressIndicator;
