// Sources Selection Modal for TestMeApp
// Comprehensive modal for browsing, searching, and selecting from previously processed files

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../theme/colors';
import sourcesRegistry from '../services/sourcesRegistry';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const SourcesModal = ({ 
  visible, 
  onClose, 
  onSelectSource, 
  title = "اختيار من المصادر المحفوظة",
  allowMultiSelect = false 
}) => {
  const [sources, setSources] = useState([]);
  const [filteredSources, setFilteredSources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSources, setSelectedSources] = useState([]);
  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'name', 'size', 'usage'
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState([]);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    if (visible) {
      loadSources();
      loadCategories();
      loadStats();
    }
  }, [visible]);

  useEffect(() => {
    filterAndSortSources();
  }, [sources, searchQuery, selectedCategory, sortBy]);

  const loadSources = async () => {
    try {
      setLoading(true);
      const allSources = await sourcesRegistry.getAllSources();
      setSources(allSources);
    } catch (error) {
      console.error('Error loading sources:', error);
      Alert.alert('خطأ', 'فشل في تحميل المصادر المحفوظة');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const allCategories = await sourcesRegistry.getAllCategories();
      setCategories(['all', ...allCategories]);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadStats = async () => {
    try {
      const registryStats = await sourcesRegistry.getRegistryStats();
      setStats(registryStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const filterAndSortSources = () => {
    let filtered = [...sources];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(source => 
        source.filename.toLowerCase().includes(query) ||
        source.description.toLowerCase().includes(query) ||
        source.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(source => source.category === selectedCategory);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.filename.localeCompare(b.filename);
        case 'size':
          return b.fileSize - a.fileSize;
        case 'usage':
          return b.usageCount - a.usageCount;
        case 'recent':
        default:
          return new Date(b.lastAccessed) - new Date(a.lastAccessed);
      }
    });

    setFilteredSources(filtered);
  };

  const handleSourceSelect = (source) => {
    if (allowMultiSelect) {
      const isSelected = selectedSources.find(s => s.hash === source.hash);
      if (isSelected) {
        setSelectedSources(selectedSources.filter(s => s.hash !== source.hash));
      } else {
        setSelectedSources([...selectedSources, source]);
      }
    } else {
      onSelectSource(source);
    }
  };

  const handleMultiSelectConfirm = () => {
    if (selectedSources.length > 0) {
      onSelectSource(selectedSources);
    }
  };

  const toggleFavorite = async (source) => {
    try {
      const newFavoriteStatus = await sourcesRegistry.toggleFavorite(source.hash);
      
      // Update local state
      setSources(sources.map(s => 
        s.hash === source.hash 
          ? { ...s, isFavorite: newFavoriteStatus }
          : s
      ));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('خطأ', 'فشل في تحديث المفضلة');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'processed':
        return { name: 'check-circle', color: COLORS.success };
      case 'uploaded':
        return { name: 'cloud-done', color: COLORS.primary };
      default:
        return { name: 'pending', color: COLORS.textSecondary };
    }
  };

  const getFileTypeIcon = (fileType) => {
    if (fileType?.includes('pdf')) return 'picture-as-pdf';
    if (fileType?.includes('image')) return 'image';
    if (fileType?.includes('text')) return 'description';
    if (fileType?.includes('word')) return 'description';
    return 'insert-drive-file';
  };

  const SourceItem = ({ source }) => {
    const statusIcon = getStatusIcon(source.status);
    const fileIcon = getFileTypeIcon(source.fileType);
    const isSelected = allowMultiSelect && selectedSources.find(s => s.hash === source.hash);

    return (
      <TouchableOpacity
        style={[
          styles.sourceItem,
          isSelected && styles.sourceItemSelected
        ]}
        onPress={() => handleSourceSelect(source)}
      >
        <View style={styles.sourceHeader}>
          <View style={styles.sourceInfo}>
            <MaterialIcons name={fileIcon} size={24} color={COLORS.primary} />
            <View style={styles.sourceDetails}>
              <Text style={styles.sourceName} numberOfLines={1}>
                {source.filename}
              </Text>
              <Text style={styles.sourceSize}>
                {source.formattedSize} • {source.formattedDate}
              </Text>
            </View>
          </View>
          
          <View style={styles.sourceActions}>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => toggleFavorite(source)}
            >
              <MaterialIcons 
                name={source.isFavorite ? "favorite" : "favorite-border"} 
                size={20} 
                color={source.isFavorite ? COLORS.error : COLORS.textSecondary} 
              />
            </TouchableOpacity>
            
            <View style={styles.statusIndicator}>
              <MaterialIcons 
                name={statusIcon.name} 
                size={16} 
                color={statusIcon.color} 
              />
            </View>
          </View>
        </View>

        {source.description && (
          <Text style={styles.sourceDescription} numberOfLines={2}>
            {source.description}
          </Text>
        )}

        {source.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {source.tags.slice(0, 3).map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            {source.tags.length > 3 && (
              <Text style={styles.moreTagsText}>+{source.tags.length - 3}</Text>
            )}
          </View>
        )}

        <View style={styles.sourceFooter}>
          <Text style={styles.usageText}>
            استُخدم {source.usageCount} مرة
          </Text>
          <Text style={styles.categoryText}>
            {source.category}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const FilterChip = ({ label, value, isSelected, onPress }) => (
    <TouchableOpacity
      style={[styles.filterChip, isSelected && styles.filterChipSelected]}
      onPress={() => onPress(value)}
    >
      <Text style={[
        styles.filterChipText,
        isSelected && styles.filterChipTextSelected
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          
          <Text style={styles.title}>{title}</Text>
          
          <TouchableOpacity 
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <MaterialIcons name="filter-list" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </View>

        {/* Stats Bar */}
        {stats && (
          <View style={styles.statsBar}>
            <Text style={styles.statsText}>
              {stats.totalSources} ملف • {stats.favoritesCount} مفضل
            </Text>
          </View>
        )}

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color={COLORS.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="البحث في الملفات..."
            placeholderTextColor={COLORS.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <MaterialIcons name="clear" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Filters */}
        {showFilters && (
          <View style={styles.filtersContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>الفئة:</Text>
                {categories.map(category => (
                  <FilterChip
                    key={category}
                    label={category === 'all' ? 'الكل' : category}
                    value={category}
                    isSelected={selectedCategory === category}
                    onPress={setSelectedCategory}
                  />
                ))}
              </View>
            </ScrollView>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>ترتيب:</Text>
                <FilterChip
                  label="الأحدث"
                  value="recent"
                  isSelected={sortBy === 'recent'}
                  onPress={setSortBy}
                />
                <FilterChip
                  label="الاسم"
                  value="name"
                  isSelected={sortBy === 'name'}
                  onPress={setSortBy}
                />
                <FilterChip
                  label="الحجم"
                  value="size"
                  isSelected={sortBy === 'size'}
                  onPress={setSortBy}
                />
                <FilterChip
                  label="الاستخدام"
                  value="usage"
                  isSelected={sortBy === 'usage'}
                  onPress={setSortBy}
                />
              </View>
            </ScrollView>
          </View>
        )}

        {/* Sources List */}
        <ScrollView style={styles.sourcesList} showsVerticalScrollIndicator={false}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>جاري التحميل...</Text>
            </View>
          ) : filteredSources.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="folder-open" size={64} color={COLORS.textSecondary} />
              <Text style={styles.emptyText}>
                {searchQuery ? 'لا توجد نتائج للبحث' : 'لا توجد ملفات محفوظة'}
              </Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'جرب كلمات بحث أخرى' : 'ابدأ برفع ملف جديد'}
              </Text>
            </View>
          ) : (
            filteredSources.map(source => (
              <SourceItem key={source.hash} source={source} />
            ))
          )}
        </ScrollView>

        {/* Multi-select Footer */}
        {allowMultiSelect && selectedSources.length > 0 && (
          <View style={styles.multiSelectFooter}>
            <Text style={styles.selectedCountText}>
              تم اختيار {selectedSources.length} ملف
            </Text>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleMultiSelectConfirm}
            >
              <Text style={styles.confirmButtonText}>تأكيد الاختيار</Text>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.background,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  filterButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.background,
  },
  statsBar: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: COLORS.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  statsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    margin: 20,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.textPrimary,
    marginLeft: 12,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginRight: 12,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginRight: 8,
  },
  filterChipSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  filterChipText: {
    fontSize: 12,
    color: COLORS.textPrimary,
  },
  filterChipTextSelected: {
    color: COLORS.textPrimary,
    fontWeight: '600',
  },
  sourcesList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sourceItem: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  sourceItemSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.background,
  },
  sourceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  sourceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sourceDetails: {
    marginLeft: 12,
    flex: 1,
  },
  sourceName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  sourceSize: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  sourceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  favoriteButton: {
    padding: 4,
  },
  statusIndicator: {
    padding: 4,
  },
  sourceDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: COLORS.background,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 10,
    color: COLORS.textSecondary,
  },
  moreTagsText: {
    fontSize: 10,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  sourceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  usageText: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  categoryText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  multiSelectFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.cardBackground,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  selectedCountText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
});

export default SourcesModal;
