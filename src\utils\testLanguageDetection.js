// Test file for language detection
import aiService from '../services/aiService';

export const testLanguageDetection = () => {
  console.log('=== Testing Language Detection ===');
  
  // Test Arabic content
  const arabicContent = `
الفصل الأول: مقدمة في الرياضيات

تعريف الرياضيات:
الرياضيات هي علم دراسة الأرقام والأشكال والأنماط. تشمل الرياضيات عدة فروع مثل الجبر والهندسة والإحصاء.

المفاهيم الأساسية:
1. الأعداد الطبيعية: 1، 2، 3، 4، ...
2. الأعداد الصحيحة: ...-2، -1، 0، 1، 2، ...
`;

  // Test English content
  const englishContent = `
Chapter 1: Introduction to Mathematics

Definition of Mathematics:
Mathematics is the science of studying numbers, shapes, and patterns. Mathematics includes several branches such as algebra, geometry, and statistics.

Basic Concepts:
1. Natural numbers: 1, 2, 3, 4, ...
2. Integers: ...-2, -1, 0, 1, 2, ...
3. Rational numbers: numbers that can be written as fractions
4. Real numbers: include all rational and irrational numbers

Basic Operations:
- Addition (+)
- Subtraction (-)
- Multiplication (×)
- Division (÷)
`;

  const arabicDetected = aiService.detectContentLanguage(arabicContent);
  const englishDetected = aiService.detectContentLanguage(englishContent);
  
  console.log('Arabic content detected as:', arabicDetected);
  console.log('English content detected as:', englishDetected);
  
  return {
    arabic: arabicDetected,
    english: englishDetected
  };
};
