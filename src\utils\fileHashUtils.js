// File Hash Utilities for TestMeApp
// Advanced file identification and duplicate detection utilities

import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

class FileHashUtils {
  constructor() {
    this.isWeb = Platform.OS === 'web';
  }

  // Generate comprehensive file fingerprint
  async generateFileFingerprint(file) {
    try {
      console.log('=== GENERATING FILE FINGERPRINT ===');
      
      const fingerprint = {
        basic: await this.generateBasicHash(file),
        content: await this.generateContentSampleHash(file),
        metadata: await this.generateMetadataHash(file),
        composite: null
      };

      // Create composite hash from all components
      const compositeString = `${fingerprint.basic}_${fingerprint.content}_${fingerprint.metadata}`;
      fingerprint.composite = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        compositeString,
        { encoding: Crypto.CryptoEncoding.HEX }
      );

      console.log('File fingerprint generated:', {
        basic: fingerprint.basic.substring(0, 8) + '...',
        content: fingerprint.content.substring(0, 8) + '...',
        metadata: fingerprint.metadata.substring(0, 8) + '...',
        composite: fingerprint.composite.substring(0, 16) + '...'
      });

      return fingerprint;
    } catch (error) {
      console.error('Error generating file fingerprint:', error);
      throw new Error('فشل في إنشاء بصمة الملف');
    }
  }

  // Generate basic file hash from metadata
  async generateBasicHash(file) {
    try {
      const basicData = {
        name: file.name,
        size: file.size,
        type: file.type || file.mimeType,
        lastModified: file.lastModified || file.modificationTime || Date.now()
      };

      const dataString = JSON.stringify(basicData);
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        dataString,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error generating basic hash:', error);
      return `basic_${file.name}_${file.size}_${Date.now()}`;
    }
  }

  // Generate hash from file content sample
  async generateContentSampleHash(file) {
    try {
      console.log('Generating content sample hash...');
      
      if (this.isWeb) {
        return await this.generateWebContentHash(file);
      } else {
        return await this.generateNativeContentHash(file);
      }
    } catch (error) {
      console.error('Error generating content sample hash:', error);
      return `content_${file.name}_${Date.now()}`;
    }
  }

  // Generate content hash for web platform
  async generateWebContentHash(file) {
    try {
      // Read first 2KB for content sampling
      const sampleSize = Math.min(file.size, 2048);
      const blob = file.slice(0, sampleSize);
      
      const arrayBuffer = await this.readBlobAsArrayBuffer(blob);
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Convert to base64 for hashing
      const base64Sample = btoa(String.fromCharCode(...uint8Array));
      
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        base64Sample,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error generating web content hash:', error);
      return `web_content_${Date.now()}`;
    }
  }

  // Generate content hash for native platform
  async generateNativeContentHash(file) {
    try {
      if (!file.uri) {
        throw new Error('File URI not available');
      }

      // For native platforms, we'll use file metadata as content hash
      // since direct file reading might not be available
      const contentData = {
        uri: file.uri,
        size: file.size,
        name: file.name,
        type: file.type || file.mimeType
      };

      const contentString = JSON.stringify(contentData);
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        contentString,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error generating native content hash:', error);
      return `native_content_${Date.now()}`;
    }
  }

  // Generate metadata hash
  async generateMetadataHash(file) {
    try {
      const metadata = {
        extension: this.getFileExtension(file.name),
        sizeCategory: this.getSizeCategory(file.size),
        typeCategory: this.getTypeCategory(file.type || file.mimeType)
      };

      const metadataString = JSON.stringify(metadata);
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        metadataString,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error generating metadata hash:', error);
      return `metadata_${Date.now()}`;
    }
  }

  // Utility: Read blob as ArrayBuffer
  readBlobAsArrayBuffer(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsArrayBuffer(blob);
    });
  }

  // Utility: Get file extension
  getFileExtension(filename) {
    return filename.split('.').pop()?.toLowerCase() || 'unknown';
  }

  // Utility: Categorize file size
  getSizeCategory(size) {
    if (size < 1024) return 'tiny';           // < 1KB
    if (size < 1024 * 1024) return 'small';  // < 1MB
    if (size < 10 * 1024 * 1024) return 'medium'; // < 10MB
    if (size < 100 * 1024 * 1024) return 'large'; // < 100MB
    return 'huge';                            // >= 100MB
  }

  // Utility: Categorize file type
  getTypeCategory(mimeType) {
    if (!mimeType) return 'unknown';
    
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
    if (mimeType.includes('text')) return 'text';
    if (mimeType.includes('image')) return 'image';
    if (mimeType.includes('video')) return 'video';
    if (mimeType.includes('audio')) return 'audio';
    
    return 'other';
  }

  // Compare two file fingerprints for similarity
  compareFingerprints(fingerprint1, fingerprint2, threshold = 0.8) {
    try {
      let matches = 0;
      let total = 0;

      // Compare basic hash
      if (fingerprint1.basic === fingerprint2.basic) matches++;
      total++;

      // Compare content hash
      if (fingerprint1.content === fingerprint2.content) matches++;
      total++;

      // Compare metadata hash
      if (fingerprint1.metadata === fingerprint2.metadata) matches++;
      total++;

      // Compare composite hash
      if (fingerprint1.composite === fingerprint2.composite) matches++;
      total++;

      const similarity = matches / total;
      const isSimilar = similarity >= threshold;

      console.log('Fingerprint comparison:', {
        similarity: similarity.toFixed(2),
        matches,
        total,
        isSimilar,
        threshold
      });

      return {
        similarity,
        isSimilar,
        matches,
        total
      };
    } catch (error) {
      console.error('Error comparing fingerprints:', error);
      return {
        similarity: 0,
        isSimilar: false,
        matches: 0,
        total: 0
      };
    }
  }

  // Generate quick hash for fast duplicate detection
  async generateQuickHash(file) {
    try {
      const quickData = `${file.name}_${file.size}_${file.type}`;
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA1,
        quickData,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error generating quick hash:', error);
      return `quick_${file.name}_${file.size}`;
    }
  }

  // Validate file for processing
  validateFile(file) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Check file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
      validation.isValid = false;
      validation.errors.push('حجم الملف كبير جداً (أكثر من 100 ميجابايت)');
    }

    // Check file size (min 1 byte)
    if (file.size === 0) {
      validation.isValid = false;
      validation.errors.push('الملف فارغ');
    }

    // Check file name
    if (!file.name || file.name.trim() === '') {
      validation.isValid = false;
      validation.errors.push('اسم الملف غير صحيح');
    }

    // Check supported file types
    const supportedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    const fileType = file.type || file.mimeType;
    if (fileType && !supportedTypes.includes(fileType)) {
      validation.warnings.push('نوع الملف قد لا يكون مدعوماً بالكامل');
    }

    // Check for potentially problematic file names
    const problematicChars = /[<>:"/\\|?*]/;
    if (problematicChars.test(file.name)) {
      validation.warnings.push('اسم الملف يحتوي على رموز قد تسبب مشاكل');
    }

    return validation;
  }

  // Generate file summary for logging
  generateFileSummary(file) {
    return {
      name: file.name,
      size: this.formatFileSize(file.size),
      type: file.type || file.mimeType || 'unknown',
      extension: this.getFileExtension(file.name),
      sizeCategory: this.getSizeCategory(file.size),
      typeCategory: this.getTypeCategory(file.type || file.mimeType)
    };
  }

  // Format file size for display
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default new FileHashUtils();
