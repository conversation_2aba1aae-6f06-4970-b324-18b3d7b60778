// AI Service for Test Me App
// Handles integration with Gemini 2.5 Flash Preview API only

import { GoogleGenerativeAI } from '@google/generative-ai';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Secure API key management
let geminiApiKey = null;
let genA<PERSON> = null;

// Initialize GoogleGenAI with stored API key
async function initializeGeminiAI() {
  if (!geminiApiKey) {
    geminiApiKey = await AsyncStorage.getItem('gemini_api_key');
  }

  if (geminiApiKey && !genAI) {
    genAI = new GoogleGenerativeAI(geminiApiKey);
  }

  return genAI;
}

// AI Model Configuration - Only Gemini 2.5 Flash Preview
const AI_MODEL = {
  name: 'Gemini 2.5 Flash Preview',
  id: 'gemini-2.5-flash-preview-05-20',
  type: 'sdk',
  supportsPDF: true,
  supportsImages: true,
  maxTokens: 8192
};

// Create AI chat function using GoogleGenAI SDK
async function createAiChat() {
  const aiInstance = await initializeGeminiAI();
  if (!aiInstance) {
    throw new Error('Gemini API key not configured. Please set your API key in settings.');
  }

  const model = aiInstance.getGenerativeModel({
    model: AI_MODEL.id,
    generationConfig: {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: AI_MODEL.maxTokens,
      candidateCount: 1,
    },
  });
  return model;
}

class AIService {
  constructor() {
    this.isInitialized = false;
  }

  // Set API key and initialize Gemini AI
  async setApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('Invalid API key provided');
    }

    geminiApiKey = apiKey;
    genAI = new GoogleGenerativeAI(apiKey);

    // Store securely in AsyncStorage
    await AsyncStorage.setItem('gemini_api_key', apiKey);
    this.isInitialized = true;

    console.log('Gemini API key set and stored securely');
  }

  // Get stored API key
  async getStoredApiKey() {
    if (!geminiApiKey) {
      geminiApiKey = await AsyncStorage.getItem('gemini_api_key');
    }
    return geminiApiKey;
  }

  // Check if API key is configured
  async isApiKeyConfigured() {
    const apiKey = await this.getStoredApiKey();
    return !!apiKey;
  }

  // Get model information
  getModelInfo() {
    return AI_MODEL;
  }

  // Get available models (for backward compatibility)
  getAvailableModels() {
    return [
      {
        key: 'gemini-2.5-flash-preview',
        name: AI_MODEL.name,
        supportsPDF: AI_MODEL.supportsPDF,
        supportsImages: AI_MODEL.supportsImages,
        priority: 1
      }
    ];
  }

  detectContentLanguage(content) {
    // Enhanced language detection based on character patterns and common words
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g;
    const englishPattern = /[a-zA-Z]/g;

    // Count Arabic and English characters
    const arabicMatches = (content.match(arabicPattern) || []).length;
    const englishMatches = (content.match(englishPattern) || []).length;

    // Check for common English words
    const commonEnglishWords = ['the', 'and', 'or', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'can', 'may', 'might', 'must', 'shall', 'do', 'does', 'did', 'get', 'got', 'make', 'made', 'take', 'took', 'come', 'came', 'go', 'went', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said', 'tell', 'told', 'give', 'gave', 'find', 'found', 'work', 'worked', 'call', 'called', 'try', 'tried', 'ask', 'asked', 'need', 'needed', 'feel', 'felt', 'become', 'became', 'leave', 'left', 'put', 'kept', 'let', 'begin', 'began'];

    // Check for common Arabic words
    const commonArabicWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'كان', 'كانت', 'يكون', 'تكون', 'هو', 'هي', 'أن', 'إن', 'لا', 'ما', 'كل', 'بعض', 'جميع', 'كيف', 'متى', 'أين', 'لماذا', 'ماذا', 'من', 'أي', 'بين', 'خلال', 'بعد', 'قبل', 'عند', 'لدى', 'حول', 'ضد', 'نحو', 'تحت', 'فوق', 'أمام', 'خلف', 'يمين', 'يسار'];

    const contentLower = content.toLowerCase();

    // Count English and Arabic word matches
    const englishWordMatches = commonEnglishWords.filter(word =>
      contentLower.includes(' ' + word + ' ') ||
      contentLower.startsWith(word + ' ') ||
      contentLower.endsWith(' ' + word)
    ).length;

    const arabicWordMatches = commonArabicWords.filter(word =>
      content.includes(' ' + word + ' ') ||
      content.includes(word + ' ') ||
      content.includes(' ' + word)
    ).length;

    // Calculate total characters (excluding spaces and numbers)
    const totalChars = arabicMatches + englishMatches;

    if (totalChars === 0) {
      return 'ar'; // Default to Arabic if no text detected
    }

    // Calculate ratios
    const arabicCharRatio = arabicMatches / totalChars;
    const englishCharRatio = englishMatches / totalChars;

    // Weight character ratio and word matches
    const arabicScore = (arabicCharRatio * 0.7) + (arabicWordMatches * 0.3);
    const englishScore = (englishCharRatio * 0.7) + (englishWordMatches * 0.3);

    console.log(`Language detection details:
    - Arabic chars: ${arabicMatches}, English chars: ${englishMatches}
    - Arabic words: ${arabicWordMatches}, English words: ${englishWordMatches}
    - Arabic score: ${arabicScore.toFixed(2)}, English score: ${englishScore.toFixed(2)}`);

    // If English score is significantly higher, it's English content
    if (englishScore > arabicScore && englishCharRatio > 0.3) {
      return 'en';
    }

    // Otherwise, default to Arabic
    return 'ar';
  }

  async generateQuestions(content, options = {}) {
    const {
      subject = 'عام',
      questionType = 'multiple_choice',
      questionCount = 10,
      contentType = 'chapter',
      specialNotes = '',
      language = 'ar'
    } = options;

    // Determine the language to use
    let finalLanguage = language;
    if (language === 'auto') {
      finalLanguage = this.detectContentLanguage(content);
      console.log('Auto-detected content language:', finalLanguage);
    } else {
      console.log('Using specified language:', finalLanguage);
    }

    // Handle max questions
    let finalQuestionCount = questionCount;
    if (questionCount === 'max') {
      // Estimate max questions based on content length
      const contentWords = content.split(/\s+/).length;
      finalQuestionCount = Math.min(Math.floor(contentWords / 50), 100); // 1 question per 50 words, max 100
      console.log(`Max questions calculated: ${finalQuestionCount} (based on ${contentWords} words)`);
    }

    const prompt = this.buildQuestionPrompt(content, {
      subject,
      questionType,
      questionCount: finalQuestionCount,
      contentType,
      specialNotes,
      language: finalLanguage
    });

    try {
      console.log('=== GENERATE QUESTIONS REQUEST ===');
      console.log('Selected model from options:', options.selectedModel);
      console.log('Will use specific model:', options.selectedModel ? 'YES' : 'NO (auto mode)');

      // Use the new AI model system
      const result = await this.callAIModel(prompt, options.selectedModel);
      return this.parseQuestionsResponse(result.response, result.modelName);
    } catch (error) {
      console.error('Error generating questions:', error);
      throw new Error('فشل في توليد الأسئلة. يرجى المحاولة مرة أخرى.');
    }
  }

  buildQuestionPrompt(content, options) {
    const {
      subject,
      questionType,
      questionCount,
      contentType,
      specialNotes,
      language
    } = options;

    // Language-specific mappings
    const questionTypeMap = {
      ar: {
        'multiple_choice': 'اختيار من متعدد',
        'true_false': 'صح أو خطأ',
        'short_answer': 'إجابة قصيرة',
        'mixed': 'مختلط'
      },
      en: {
        'multiple_choice': 'Multiple Choice',
        'true_false': 'True or False',
        'short_answer': 'Short Answer',
        'mixed': 'Mixed'
      },
      fr: {
        'multiple_choice': 'Choix Multiple',
        'true_false': 'Vrai ou Faux',
        'short_answer': 'Réponse Courte',
        'mixed': 'Mixte'
      }
    };

    const contentTypeMap = {
      ar: {
        'chapter': 'فصل واحد',
        'semester': 'فصل دراسي',
        'book': 'كتاب كامل',
        'exam': 'أسئلة امتحان'
      },
      en: {
        'chapter': 'Single Chapter',
        'semester': 'Semester Material',
        'book': 'Complete Book',
        'exam': 'Exam Questions'
      },
      fr: {
        'chapter': 'Chapitre Unique',
        'semester': 'Matériel de Semestre',
        'book': 'Livre Complet',
        'exam': 'Questions d\'Examen'
      }
    };

    if (language === 'en') {
      return `
You are an advanced AI educational assistant powered by Gemini 2.5 Flash Preview, specialized in creating comprehensive educational assessments. Your task is to meticulously analyze the provided content and generate high-quality, contextually accurate questions.

## Test Configuration:
- **Subject**: ${subject}
- **Question Type**: ${questionTypeMap[language][questionType]}
- **Question Count**: ${questionCount}
- **Content Type**: ${contentTypeMap[language][contentType]}
- **Special Instructions**: ${specialNotes || 'Standard assessment'}

## Source Content:
${content}

## Critical Instructions:
1. **Content Fidelity**: Extract questions EXCLUSIVELY from the provided content
2. **Linguistic Accuracy**: Generate all content in English with proper grammar and terminology
3. **Cognitive Levels**: Create questions spanning Bloom's taxonomy (Remember, Understand, Apply, Analyze)
4. **Question Quality**: Ensure each question tests genuine comprehension, not memorization
5. **Distractor Quality**: Design plausible incorrect options that test common misconceptions
6. **Evidence-Based**: Every correct answer must be directly traceable to the source content

## Advanced Requirements:
- **Contextual Depth**: Questions should demonstrate understanding of relationships between concepts
- **Academic Rigor**: Maintain appropriate difficulty progression
- **Comprehensive Coverage**: Address key themes, definitions, processes, and applications from the content
- **Precision**: Avoid ambiguous wording or trick questions

## Response Format (Strict JSON):
{
  "questions": [
    {
      "id": 1,
      "type": "${questionType}",
      "question": "Precisely worded question derived from content",
      "options": ["Correct answer", "Plausible distractor 1", "Plausible distractor 2", "Plausible distractor 3"],
      "correctAnswer": 0,
      "explanation": "Detailed explanation with direct content reference",
      "reference": "Exact quote or paraphrase from source material",
      "difficulty": "easy|medium|hard",
      "bloomsLevel": "remember|understand|apply|analyze"
    }
  ]
}

## Quality Assurance:
- Verify each question can be answered solely from the provided content
- Ensure explanations include specific references to source material
- Confirm all options are grammatically consistent and appropriately challenging
${specialNotes ? `- Additional requirement: ${specialNotes}` : ''}

Generate ONLY the JSON response without any additional text or formatting:
`;
    } else if (language === 'fr') {
      return `
Vous êtes un assistant IA avancé alimenté par Gemini 2.5 Pro, spécialisé dans la création d'évaluations éducatives complètes. Votre mission est d'analyser méticuleusement le contenu fourni et de générer des questions de haute qualité, contextuellement précises.

## Configuration du Test:
- **Matière**: ${subject}
- **Type de Questions**: ${questionTypeMap[language][questionType]}
- **Nombre de Questions**: ${questionCount}
- **Type de Contenu**: ${contentTypeMap[language][contentType]}
- **Instructions Spéciales**: ${specialNotes || 'Évaluation standard'}

## Contenu Source:
${content}

## Instructions Critiques:
1. **Fidélité au Contenu**: Extraire les questions EXCLUSIVEMENT du contenu fourni
2. **Précision Linguistique**: Générer tout le contenu en français avec une grammaire et une terminologie appropriées
3. **Niveaux Cognitifs**: Créer des questions couvrant la taxonomie de Bloom (Mémoriser, Comprendre, Appliquer, Analyser)
4. **Qualité des Questions**: S'assurer que chaque question teste une véritable compréhension, pas la mémorisation
5. **Qualité des Distracteurs**: Concevoir des options incorrectes plausibles qui testent les idées fausses communes
6. **Basé sur les Preuves**: Chaque réponse correcte doit être directement traçable au contenu source

## Exigences Avancées:
- **Profondeur Contextuelle**: Les questions doivent démontrer la compréhension des relations entre concepts
- **Rigueur Académique**: Maintenir une progression de difficulté appropriée
- **Couverture Complète**: Aborder les thèmes clés, définitions, processus et applications du contenu
- **Précision**: Éviter les formulations ambiguës ou les questions piège

## Format de Réponse (JSON Strict):
{
  "questions": [
    {
      "id": 1,
      "type": "${questionType}",
      "question": "Question formulée avec précision dérivée du contenu",
      "options": ["Réponse correcte", "Distracteur plausible 1", "Distracteur plausible 2", "Distracteur plausible 3"],
      "correctAnswer": 0,
      "explanation": "Explication détaillée avec référence directe au contenu",
      "reference": "Citation exacte ou paraphrase du matériel source",
      "difficulty": "easy|medium|hard",
      "bloomsLevel": "remember|understand|apply|analyze"
    }
  ]
}

## Assurance Qualité:
- Vérifier que chaque question peut être répondue uniquement à partir du contenu fourni
- S'assurer que les explications incluent des références spécifiques au matériel source
- Confirmer que toutes les options sont grammaticalement cohérentes et appropriément challengeantes
${specialNotes ? `- Exigence supplémentaire: ${specialNotes}` : ''}

Générer UNIQUEMENT la réponse JSON sans texte ou formatage supplémentaire:
`;
    } else {
      return `
أنت مساعد ذكي متقدم مدعوم بتقنية Gemini 2.5 Pro، متخصص في إنشاء تقييمات تعليمية شاملة. مهمتك هي تحليل المحتوى المقدم بدقة متناهية وتوليد أسئلة عالية الجودة ودقيقة السياق.

## إعدادات الاختبار:
- **المادة**: ${subject}
- **نوع الأسئلة**: ${questionTypeMap[language][questionType]}
- **عدد الأسئلة**: ${questionCount}
- **نوع المحتوى**: ${contentTypeMap[language][contentType]}
- **تعليمات خاصة**: ${specialNotes || 'تقييم معياري'}

## المحتوى المصدر:
${content}

## التعليمات الحاسمة:
1. **دقة المحتوى**: استخرج الأسئلة حصرياً من المحتوى المقدم
2. **الدقة اللغوية**: أنشئ جميع المحتويات بالعربية مع قواعد نحوية ومصطلحات صحيحة
3. **المستويات المعرفية**: أنشئ أسئلة تغطي تصنيف بلوم (التذكر، الفهم، التطبيق، التحليل)
4. **جودة الأسئلة**: تأكد من أن كل سؤال يختبر الفهم الحقيقي وليس الحفظ
5. **جودة المشتتات**: صمم خيارات خاطئة معقولة تختبر المفاهيم الخاطئة الشائعة
6. **مبني على الأدلة**: كل إجابة صحيحة يجب أن تكون قابلة للتتبع مباشرة إلى المحتوى المصدر

## المتطلبات المتقدمة:
- **العمق السياقي**: الأسئلة يجب أن تظهر فهم العلاقات بين المفاهيم
- **الصرامة الأكاديمية**: حافظ على تدرج مناسب في الصعوبة
- **التغطية الشاملة**: تناول الموضوعات الرئيسية والتعريفات والعمليات والتطبيقات من المحتوى
- **الدقة**: تجنب الصياغة الغامضة أو الأسئلة المخادعة

## تنسيق الاستجابة (JSON صارم):
{
  "questions": [
    {
      "id": 1,
      "type": "${questionType}",
      "question": "سؤال مصاغ بدقة مستمد من المحتوى",
      "options": ["الإجابة الصحيحة", "مشتت معقول 1", "مشتت معقول 2", "مشتت معقول 3"],
      "correctAnswer": 0,
      "explanation": "تفسير مفصل مع مرجع مباشر للمحتوى",
      "reference": "اقتباس دقيق أو إعادة صياغة من المادة المصدر",
      "difficulty": "easy|medium|hard",
      "bloomsLevel": "remember|understand|apply|analyze"
    }
  ]
}

## ضمان الجودة:
- تحقق من أن كل سؤال يمكن الإجابة عليه فقط من المحتوى المقدم
- تأكد من أن التفسيرات تتضمن مراجع محددة للمادة المصدر
- أكد أن جميع الخيارات متسقة نحوياً ومتحدية بشكل مناسب
${specialNotes ? `- متطلب إضافي: ${specialNotes}` : ''}

أنشئ فقط استجابة JSON بدون أي نص أو تنسيق إضافي:
`;
    }
  }

  async callAIModel(prompt, maxRetries = 3) {
    console.log('=== CALLING GEMINI 2.5 FLASH PREVIEW ===');
    console.log('Prompt length:', prompt.length);

    // Check if API key is configured
    if (!(await this.isApiKeyConfigured())) {
      throw new Error('Gemini API key not configured. Please set your API key in settings.');
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Calling Gemini (attempt ${attempt}/${maxRetries})...`);

        // Use GoogleGenAI SDK
        const aiModel = await createAiChat();
        const result = await aiModel.generateContent(prompt);
        const response = result.response.text();

        if (response && response.trim()) {
          console.log('Gemini API call successful');
          return { response, modelName: AI_MODEL.name };
        } else {
          throw new Error('Empty response from Gemini');
        }
      } catch (error) {
        console.error(`Gemini attempt ${attempt} failed:`, error);

        // Handle rate limiting
        if (error.message?.includes('429') || error.message?.includes('quota')) {
          if (attempt < maxRetries) {
            console.log(`Rate limited, retrying in ${2000 * attempt}ms...`);
            await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            continue;
          }
        }

        // Handle API key errors
        if (error.message?.includes('API_KEY_INVALID') || error.message?.includes('401')) {
          throw new Error('Invalid Gemini API key. Please check your API key in settings.');
        }

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        } else {
          throw error;
        }
      }
    }

    throw new Error('Gemini API failed to generate response after all retries');
  }



  parseQuestionsResponse(response, modelName = 'AI Model') {
    try {
      console.log('Raw AI response:', response);

      // Clean the response - remove any markdown formatting
      let cleanResponse = response.trim();

      // Remove markdown code blocks if present
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to extract JSON from the response
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        console.log('Extracted JSON:', jsonStr);

        const parsed = JSON.parse(jsonStr);

        if (parsed.questions && Array.isArray(parsed.questions)) {
          console.log('Successfully parsed questions:', parsed.questions.length);

          // Validate and clean the questions with enhanced fields
          const validQuestions = parsed.questions.map((q, index) => ({
            id: q.id || (index + 1),
            type: q.type || 'multiple_choice',
            question: q.question || `سؤال ${index + 1}`,
            options: Array.isArray(q.options) ? q.options : ['خيار 1', 'خيار 2', 'خيار 3', 'خيار 4'],
            correctAnswer: typeof q.correctAnswer === 'number' ? q.correctAnswer : 0,
            explanation: q.explanation || 'لا يوجد تفسير متاح',
            reference: q.reference || 'المحتوى المرفق',
            difficulty: q.difficulty || 'medium',
            bloomsLevel: q.bloomsLevel || 'understand',
            generatedBy: modelName,
            timestamp: new Date().toISOString()
          }));

          return validQuestions;
        }
      }

      // If JSON parsing fails, throw an error instead of using mock data
      console.error('Failed to parse AI response. Response was:', response);
      throw new Error('فشل في تحليل استجابة الذكاء الاصطناعي. يرجى المحاولة مرة أخرى.');
    } catch (error) {
      console.error('Error parsing questions response:', error);
      console.error('Response was:', response);
      throw new Error('فشل في معالجة الأسئلة المولدة. يرجى المحاولة مرة أخرى.');
    }
  }

  // Mock questions function removed to ensure only real content is used

  async explainQuestion(question, userAnswer, correctAnswer) {
    const prompt = `
أنت مساعد تعليمي ذكي مدعوم بتقنية Gemini 2.5 Flash Preview. مهمتك هي شرح السؤال التالي والإجابة الصحيحة للطالب.

السؤال: ${question.question}
الخيارات: ${question.options.join(', ')}
إجابة الطالب: ${question.options[userAnswer]}
الإجابة الصحيحة: ${question.options[correctAnswer]}

قدم شرحاً مفصلاً وواضحاً يساعد الطالب على فهم:
1. لماذا الإجابة الصحيحة صحيحة
2. لماذا إجابته خاطئة (إن كانت كذلك)
3. المفهوم الأساسي وراء السؤال
4. نصائح لتذكر هذه المعلومة

استخدم لغة بسيطة ومشجعة.
`;

    try {
      const result = await this.callAIModel(prompt);
      return result.response;
    } catch (error) {
      console.error('Error explaining question:', error);
      return 'عذراً، لا يمكن تقديم الشرح في الوقت الحالي. يرجى المحاولة مرة أخرى لاحقاً.';
    }
  }
}

export default new AIService();
