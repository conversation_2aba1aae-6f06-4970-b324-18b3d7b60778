// Unified File Caching Service for TestMeApp
// Handles file storage, duplicate detection, and cross-screen data sharing

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';

class FileCacheService {
  constructor() {
    this.CACHE_KEY = 'testme_file_cache';
    this.CACHE_EXPIRY_DAYS = 90;
    this.MAX_CACHE_SIZE_MB = 50;
    this.MAX_CACHE_ENTRIES = 20;
  }

  // Generate content-based hash for file identification
  async generateContentBasedHash(file) {
    try {
      console.log('=== GENERATING FILE HASH ===');
      console.log('File:', file.name, file.size, file.type);

      // Create a unique identifier based on file properties
      const fileData = {
        name: file.name,
        size: file.size,
        type: file.type || file.mimeType,
        lastModified: file.lastModified || Date.now()
      };

      // For web platform, try to read file content sample
      if (file instanceof File) {
        try {
          // Read first 1KB for content hash
          const sampleSize = Math.min(file.size, 1024);
          const blob = file.slice(0, sampleSize);
          const arrayBuffer = await this.readBlobAsArrayBuffer(blob);
          const uint8Array = new Uint8Array(arrayBuffer);
          
          // Convert to base64 for hashing
          const base64Sample = btoa(String.fromCharCode(...uint8Array));
          fileData.contentSample = base64Sample;
        } catch (error) {
          console.log('Could not read file content for hash:', error);
        }
      }

      const dataString = JSON.stringify(fileData);
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        dataString,
        { encoding: Crypto.CryptoEncoding.HEX }
      );

      console.log('Generated hash:', hash.substring(0, 16) + '...');
      return hash;
    } catch (error) {
      console.error('Error generating file hash:', error);
      // Fallback hash based on basic properties
      return `fallback_${file.name}_${file.size}_${Date.now()}`;
    }
  }

  // Helper function to read blob as ArrayBuffer
  readBlobAsArrayBuffer(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsArrayBuffer(blob);
    });
  }

  // Check if file is duplicate
  async isDuplicateFile(fileHash) {
    try {
      const cache = await this.getCache();
      const entry = cache[fileHash];
      
      if (!entry) {
        return false;
      }

      // Check if entry is expired
      const now = new Date();
      const entryDate = new Date(entry.processedAt);
      const daysDiff = (now - entryDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff > this.CACHE_EXPIRY_DAYS) {
        console.log('Cache entry expired, removing...');
        await this.removeCacheEntry(fileHash);
        return false;
      }

      console.log('Duplicate file found in cache');
      return true;
    } catch (error) {
      console.error('Error checking duplicate file:', error);
      return false;
    }
  }

  // Cache file content and processing results
  async cacheFileContent(fileHash, file, extractedText, generatedQuestions = null) {
    try {
      console.log('=== CACHING FILE CONTENT ===');
      
      const cache = await this.getCache();
      
      // Create cache entry
      const cacheEntry = {
        fileHash,
        filename: file.name,
        fileSize: file.size,
        fileType: file.type || file.mimeType,
        extractedText,
        generatedQuestions,
        processedAt: new Date().toISOString(),
        lastAccessed: new Date().toISOString()
      };

      // Add to cache
      cache[fileHash] = cacheEntry;

      // Clean up cache if needed
      await this.cleanupCache(cache);

      // Save updated cache
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      
      console.log('File content cached successfully');
      return true;
    } catch (error) {
      console.error('Error caching file content:', error);
      return false;
    }
  }

  // Get cached file content
  async getCachedFileContent(fileHash) {
    try {
      const cache = await this.getCache();
      const entry = cache[fileHash];
      
      if (!entry) {
        return null;
      }

      // Update last accessed time
      entry.lastAccessed = new Date().toISOString();
      cache[fileHash] = entry;
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));

      console.log('Retrieved cached file content');
      return entry;
    } catch (error) {
      console.error('Error getting cached file content:', error);
      return null;
    }
  }

  // Get all cached files (for cross-screen sharing)
  async getAllCachedFiles() {
    try {
      const cache = await this.getCache();
      const files = Object.values(cache).map(entry => ({
        hash: entry.fileHash,
        filename: entry.filename,
        fileSize: entry.fileSize,
        fileType: entry.fileType,
        processedAt: entry.processedAt,
        lastAccessed: entry.lastAccessed,
        hasExtractedText: !!entry.extractedText,
        hasQuestions: !!entry.generatedQuestions
      }));

      // Sort by last accessed (most recent first)
      files.sort((a, b) => new Date(b.lastAccessed) - new Date(a.lastAccessed));
      
      return files;
    } catch (error) {
      console.error('Error getting all cached files:', error);
      return [];
    }
  }

  // Get cache statistics
  async getCacheStats() {
    try {
      const cache = await this.getCache();
      const entries = Object.values(cache);
      
      const now = new Date();
      let totalSize = 0;
      let activeCount = 0;
      let expiredCount = 0;

      entries.forEach(entry => {
        const entryDate = new Date(entry.processedAt);
        const daysDiff = (now - entryDate) / (1000 * 60 * 60 * 24);
        
        if (daysDiff <= this.CACHE_EXPIRY_DAYS) {
          activeCount++;
          totalSize += entry.extractedText ? entry.extractedText.length : 0;
        } else {
          expiredCount++;
        }
      });

      return {
        totalEntries: entries.length,
        activeCount,
        expiredCount,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2)
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        totalEntries: 0,
        activeCount: 0,
        expiredCount: 0,
        totalSizeMB: '0.00'
      };
    }
  }

  // Clear all cache
  async clearAllCache() {
    try {
      await AsyncStorage.removeItem(this.CACHE_KEY);
      console.log('All cache cleared');
      return true;
    } catch (error) {
      console.error('Error clearing cache:', error);
      return false;
    }
  }

  // Remove specific cache entry
  async removeCacheEntry(fileHash) {
    try {
      const cache = await this.getCache();
      delete cache[fileHash];
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      return true;
    } catch (error) {
      console.error('Error removing cache entry:', error);
      return false;
    }
  }

  // Get cache from storage
  async getCache() {
    try {
      const cacheData = await AsyncStorage.getItem(this.CACHE_KEY);
      return cacheData ? JSON.parse(cacheData) : {};
    } catch (error) {
      console.error('Error getting cache:', error);
      return {};
    }
  }

  // Clean up expired entries and manage cache size
  async cleanupCache(cache) {
    try {
      const now = new Date();
      const entries = Object.entries(cache);
      
      // Remove expired entries
      const activeEntries = entries.filter(([hash, entry]) => {
        const entryDate = new Date(entry.processedAt);
        const daysDiff = (now - entryDate) / (1000 * 60 * 60 * 24);
        return daysDiff <= this.CACHE_EXPIRY_DAYS;
      });

      // If still too many entries, remove oldest ones
      if (activeEntries.length > this.MAX_CACHE_ENTRIES) {
        activeEntries.sort((a, b) => 
          new Date(a[1].lastAccessed) - new Date(b[1].lastAccessed)
        );
        activeEntries.splice(0, activeEntries.length - this.MAX_CACHE_ENTRIES);
      }

      // Rebuild cache with cleaned entries
      const cleanedCache = {};
      activeEntries.forEach(([hash, entry]) => {
        cleanedCache[hash] = entry;
      });

      // Update the cache reference
      Object.keys(cache).forEach(key => delete cache[key]);
      Object.assign(cache, cleanedCache);

      console.log(`Cache cleaned: ${activeEntries.length} entries remaining`);
    } catch (error) {
      console.error('Error cleaning cache:', error);
    }
  }

  // Format file size for display
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default new FileCacheService();
