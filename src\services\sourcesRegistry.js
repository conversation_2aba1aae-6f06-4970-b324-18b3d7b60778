// Sources Registry Service for TestMeApp
// Unified registry for previously processed files accessible across all screens

import AsyncStorage from '@react-native-async-storage/async-storage';
import fileCacheService from './fileCacheService';

class SourcesRegistry {
  constructor() {
    this.REGISTRY_KEY = 'testme_sources_registry';
    this.MAX_RECENT_SOURCES = 50;
  }

  // Get all sources from registry
  async getAllSources() {
    try {
      console.log('=== GETTING ALL SOURCES FROM REGISTRY ===');
      
      // Get cached files from file cache service
      const cachedFiles = await fileCacheService.getAllCachedFiles();
      
      // Get registry metadata
      const registry = await this.getRegistry();
      
      // Combine and enrich data
      const sources = cachedFiles.map(file => {
        const registryEntry = registry[file.hash] || {};
        
        return {
          hash: file.hash,
          filename: file.filename,
          fileSize: file.fileSize,
          fileType: file.fileType,
          processedAt: file.processedAt,
          lastAccessed: file.lastAccessed,
          hasExtractedText: file.hasExtractedText,
          hasQuestions: file.hasQuestions,
          
          // Registry metadata
          tags: registryEntry.tags || [],
          category: registryEntry.category || 'uncategorized',
          description: registryEntry.description || '',
          usageCount: registryEntry.usageCount || 0,
          lastUsedIn: registryEntry.lastUsedIn || 'unknown',
          isFavorite: registryEntry.isFavorite || false,
          
          // Computed properties
          formattedSize: this.formatFileSize(file.fileSize),
          formattedDate: this.formatDate(file.lastAccessed),
          status: this.getSourceStatus(file)
        };
      });

      // Sort by last accessed (most recent first)
      sources.sort((a, b) => new Date(b.lastAccessed) - new Date(a.lastAccessed));
      
      console.log(`Retrieved ${sources.length} sources from registry`);
      return sources;
    } catch (error) {
      console.error('Error getting all sources:', error);
      return [];
    }
  }

  // Get sources by category
  async getSourcesByCategory(category) {
    try {
      const allSources = await this.getAllSources();
      return allSources.filter(source => source.category === category);
    } catch (error) {
      console.error('Error getting sources by category:', error);
      return [];
    }
  }

  // Get recent sources
  async getRecentSources(limit = 10) {
    try {
      const allSources = await this.getAllSources();
      return allSources.slice(0, limit);
    } catch (error) {
      console.error('Error getting recent sources:', error);
      return [];
    }
  }

  // Get favorite sources
  async getFavoriteSources() {
    try {
      const allSources = await this.getAllSources();
      return allSources.filter(source => source.isFavorite);
    } catch (error) {
      console.error('Error getting favorite sources:', error);
      return [];
    }
  }

  // Search sources
  async searchSources(query) {
    try {
      const allSources = await this.getAllSources();
      const lowercaseQuery = query.toLowerCase();
      
      return allSources.filter(source => 
        source.filename.toLowerCase().includes(lowercaseQuery) ||
        source.description.toLowerCase().includes(lowercaseQuery) ||
        source.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
        source.category.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Error searching sources:', error);
      return [];
    }
  }

  // Update source metadata
  async updateSourceMetadata(fileHash, metadata) {
    try {
      console.log('=== UPDATING SOURCE METADATA ===');
      console.log('File hash:', fileHash.substring(0, 16) + '...');
      console.log('Metadata:', metadata);
      
      const registry = await this.getRegistry();
      
      // Update or create registry entry
      registry[fileHash] = {
        ...registry[fileHash],
        ...metadata,
        updatedAt: new Date().toISOString()
      };
      
      await this.saveRegistry(registry);
      console.log('Source metadata updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating source metadata:', error);
      return false;
    }
  }

  // Record source usage
  async recordSourceUsage(fileHash, usedIn) {
    try {
      const registry = await this.getRegistry();
      const entry = registry[fileHash] || {};
      
      registry[fileHash] = {
        ...entry,
        usageCount: (entry.usageCount || 0) + 1,
        lastUsedIn: usedIn,
        lastUsedAt: new Date().toISOString()
      };
      
      await this.saveRegistry(registry);
      console.log(`Recorded usage for source in ${usedIn}`);
      return true;
    } catch (error) {
      console.error('Error recording source usage:', error);
      return false;
    }
  }

  // Toggle favorite status
  async toggleFavorite(fileHash) {
    try {
      const registry = await this.getRegistry();
      const entry = registry[fileHash] || {};
      
      registry[fileHash] = {
        ...entry,
        isFavorite: !entry.isFavorite,
        updatedAt: new Date().toISOString()
      };
      
      await this.saveRegistry(registry);
      console.log(`Toggled favorite status for source`);
      return registry[fileHash].isFavorite;
    } catch (error) {
      console.error('Error toggling favorite:', error);
      return false;
    }
  }

  // Add tags to source
  async addTagsToSource(fileHash, tags) {
    try {
      const registry = await this.getRegistry();
      const entry = registry[fileHash] || {};
      
      const existingTags = entry.tags || [];
      const newTags = tags.filter(tag => !existingTags.includes(tag));
      
      registry[fileHash] = {
        ...entry,
        tags: [...existingTags, ...newTags],
        updatedAt: new Date().toISOString()
      };
      
      await this.saveRegistry(registry);
      console.log(`Added tags to source:`, newTags);
      return true;
    } catch (error) {
      console.error('Error adding tags to source:', error);
      return false;
    }
  }

  // Remove tags from source
  async removeTagsFromSource(fileHash, tagsToRemove) {
    try {
      const registry = await this.getRegistry();
      const entry = registry[fileHash] || {};
      
      const existingTags = entry.tags || [];
      const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));
      
      registry[fileHash] = {
        ...entry,
        tags: filteredTags,
        updatedAt: new Date().toISOString()
      };
      
      await this.saveRegistry(registry);
      console.log(`Removed tags from source:`, tagsToRemove);
      return true;
    } catch (error) {
      console.error('Error removing tags from source:', error);
      return false;
    }
  }

  // Get all unique tags
  async getAllTags() {
    try {
      const registry = await this.getRegistry();
      const allTags = new Set();
      
      Object.values(registry).forEach(entry => {
        if (entry.tags) {
          entry.tags.forEach(tag => allTags.add(tag));
        }
      });
      
      return Array.from(allTags).sort();
    } catch (error) {
      console.error('Error getting all tags:', error);
      return [];
    }
  }

  // Get all unique categories
  async getAllCategories() {
    try {
      const registry = await this.getRegistry();
      const categories = new Set();
      
      Object.values(registry).forEach(entry => {
        if (entry.category) {
          categories.add(entry.category);
        }
      });
      
      // Add default categories
      categories.add('uncategorized');
      categories.add('documents');
      categories.add('images');
      categories.add('text');
      categories.add('educational');
      categories.add('reference');
      
      return Array.from(categories).sort();
    } catch (error) {
      console.error('Error getting all categories:', error);
      return ['uncategorized'];
    }
  }

  // Get registry statistics
  async getRegistryStats() {
    try {
      const allSources = await this.getAllSources();
      const registry = await this.getRegistry();
      
      const stats = {
        totalSources: allSources.length,
        totalUsage: 0,
        favoritesCount: 0,
        categoriesCount: 0,
        tagsCount: 0,
        
        // By category
        byCategory: {},
        
        // By file type
        byFileType: {},
        
        // Recent activity
        recentActivity: allSources.slice(0, 5).map(source => ({
          filename: source.filename,
          lastAccessed: source.formattedDate,
          usageCount: source.usageCount
        }))
      };
      
      // Calculate statistics
      const categories = new Set();
      const tags = new Set();
      const fileTypes = {};
      
      allSources.forEach(source => {
        stats.totalUsage += source.usageCount;
        
        if (source.isFavorite) {
          stats.favoritesCount++;
        }
        
        categories.add(source.category);
        
        if (source.tags) {
          source.tags.forEach(tag => tags.add(tag));
        }
        
        const fileType = source.fileType || 'unknown';
        fileTypes[fileType] = (fileTypes[fileType] || 0) + 1;
        
        stats.byCategory[source.category] = (stats.byCategory[source.category] || 0) + 1;
      });
      
      stats.categoriesCount = categories.size;
      stats.tagsCount = tags.size;
      stats.byFileType = fileTypes;
      
      return stats;
    } catch (error) {
      console.error('Error getting registry stats:', error);
      return {
        totalSources: 0,
        totalUsage: 0,
        favoritesCount: 0,
        categoriesCount: 0,
        tagsCount: 0,
        byCategory: {},
        byFileType: {},
        recentActivity: []
      };
    }
  }

  // Remove source from registry
  async removeSource(fileHash) {
    try {
      const registry = await this.getRegistry();
      delete registry[fileHash];
      await this.saveRegistry(registry);
      
      // Also remove from file cache
      await fileCacheService.removeCacheEntry(fileHash);
      
      console.log('Source removed from registry and cache');
      return true;
    } catch (error) {
      console.error('Error removing source:', error);
      return false;
    }
  }

  // Clear all sources
  async clearAllSources() {
    try {
      await AsyncStorage.removeItem(this.REGISTRY_KEY);
      await fileCacheService.clearAllCache();
      console.log('All sources cleared from registry and cache');
      return true;
    } catch (error) {
      console.error('Error clearing all sources:', error);
      return false;
    }
  }

  // Helper: Get registry from storage
  async getRegistry() {
    try {
      const registryData = await AsyncStorage.getItem(this.REGISTRY_KEY);
      return registryData ? JSON.parse(registryData) : {};
    } catch (error) {
      console.error('Error getting registry:', error);
      return {};
    }
  }

  // Helper: Save registry to storage
  async saveRegistry(registry) {
    try {
      await AsyncStorage.setItem(this.REGISTRY_KEY, JSON.stringify(registry));
      return true;
    } catch (error) {
      console.error('Error saving registry:', error);
      return false;
    }
  }

  // Helper: Get source status
  getSourceStatus(file) {
    if (file.hasQuestions) {
      return 'processed';
    } else if (file.hasExtractedText) {
      return 'uploaded';
    } else {
      return 'pending';
    }
  }

  // Helper: Format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper: Format date
  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return 'اليوم';
      } else if (diffDays === 2) {
        return 'أمس';
      } else if (diffDays <= 7) {
        return `منذ ${diffDays} أيام`;
      } else {
        return date.toLocaleDateString('ar-SA');
      }
    } catch (error) {
      return 'غير محدد';
    }
  }
}

export default new SourcesRegistry();
