// Unified Design System Colors - Restricted Palette Only
// Using ONLY the 7 specified colors for the entire app

export const COLORS = {
  // Primary Background
  background: '#0E081A', // Primary app background

  // Text Colors
  textPrimary: '#FFFFFF', // Primary white text
  textSecondary: '#A88FDB', // Light purple for secondary text and percentages

  // Interactive Elements
  primary: '#6F2CFF', // Primary purple for buttons, links, interactive elements

  // Status Colors
  success: '#00D88A', // Green for success states, correct answers
  error: '#FF4F64', // Red for error states, incorrect answers

  // Card and Container Backgrounds
  cardBackground: '#1A102B', // Background for cards and content containers

  // Progress and Gradients
  progressStart: '#6F2CFF', // Progress bar gradient start
  progressEnd: '#3D2A72', // Progress bar gradient end

  // Derived Colors Using ONLY Specified Palette
  // Surface and Interactive States
  surface: '#1A102B', // Same as cardBackground for consistency
  surfaceHover: '#6F2CFF', // Primary purple for hover states
  surfaceActive: '#6F2CFF', // Primary purple for active states
  surfaceDisabled: '#A88FDB', // Light purple for disabled states
  cardBackgroundHover: '#6F2CFF', // Primary purple for card hover
  cardBackgroundActive: '#6F2CFF', // Primary purple for card active

  // Text on Different Backgrounds
  onPrimary: '#FFFFFF', // White text on primary purple
  onBackground: '#FFFFFF', // White text on main background
  onCard: '#FFFFFF', // White text on card backgrounds
  onSuccess: '#FFFFFF', // White text on success green
  onError: '#FFFFFF', // White text on error red
  textTertiary: '#A88FDB', // Light purple for tertiary text
  textDisabled: '#A88FDB', // Light purple for disabled text
  textPlaceholder: '#A88FDB', // Light purple for placeholder text

  // Border and Outline Colors (using specified palette only)
  border: '#A88FDB', // Light purple for borders
  borderLight: '#A88FDB', // Light purple for light borders
  borderSubtle: '#1A102B', // Card color for subtle borders
  outline: '#6F2CFF', // Primary purple for outlines
  outlineVariant: '#A88FDB', // Light purple for outline variants

  // Icon and Interactive Colors (restricted palette)
  iconBackground: '#1A102B', // Card background for icons
  iconBackgroundHover: '#6F2CFF', // Primary purple for icon hover
  iconPrimary: '#6F2CFF', // Primary purple for main icons
  iconSecondary: '#A88FDB', // Light purple for secondary icons

  // Shadow and Overlay (using background color)
  shadow: '#0E081A', // Background color for shadows
  overlay: 'rgba(14, 8, 26, 0.8)', // Background color overlay
  overlayLight: 'rgba(14, 8, 26, 0.6)', // Light background overlay

  // Quiz Specific Colors (using specified palette)
  quizCorrect: '#00D88A', // Green for correct answers
  quizIncorrect: '#FF4F64', // Red for incorrect answers
  quizSelected: '#6F2CFF', // Primary purple for selected options
  quizTimer: '#A88FDB', // Light purple for timer
  quizTimerDanger: '#FF4F64', // Red for timer danger

  // Additional colors (restricted palette)
  divider: '#A88FDB', // Light purple for divider lines
  dividerSubtle: '#1A102B', // Card color for subtle dividers
  backdrop: 'rgba(14, 8, 26, 0.9)', // Background color backdrop
  highlight: 'rgba(111, 44, 255, 0.3)', // Primary purple highlight
  highlightStrong: 'rgba(111, 44, 255, 0.5)', // Strong primary purple highlight

  // Interactive variants (restricted palette)
  interactivePrimary: '#6F2CFF', // Primary interactive
  interactiveSecondary: '#A88FDB', // Light purple secondary interactive
  interactiveHover: '#6F2CFF', // Primary purple hover state
  interactiveActive: '#6F2CFF', // Primary purple active state
  interactiveDisabled: '#A88FDB', // Light purple disabled state

  // Gradients using specified colors
  gradientStart: '#6F2CFF', // Primary purple gradient start
  gradientEnd: '#3D2A72', // Progress gradient end
  backgroundGradientStart: '#0E081A', // Background gradient start
  backgroundGradientEnd: '#1A102B', // Card color gradient end
};

// Spacing System
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Border Radius System
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

// Typography System
export const TYPOGRAPHY = {
  displayLarge: {
    fontSize: 32,
    fontWeight: '700',
    lineHeight: 40,
  },
  displayMedium: {
    fontSize: 28,
    fontWeight: '700',
    lineHeight: 36,
  },
  displaySmall: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 32,
  },
  headlineLarge: {
    fontSize: 22,
    fontWeight: '600',
    lineHeight: 28,
  },
  headlineMedium: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 26,
  },
  headlineSmall: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 24,
  },
  titleLarge: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
  },
  titleMedium: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 20,
  },
  titleSmall: {
    fontSize: 12,
    fontWeight: '600',
    lineHeight: 16,
  },
  bodyLarge: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
  },
  bodyMedium: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  bodySmall: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 16,
  },
  labelLarge: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  labelMedium: {
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 16,
  },
  labelSmall: {
    fontSize: 10,
    fontWeight: '500',
    lineHeight: 14,
  },
};

// Shadow System
export const SHADOWS = {
  small: {
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};
