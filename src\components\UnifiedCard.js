import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../theme/colors';

const UnifiedCard = ({
  children,
  variant = 'default', // default, elevated, outlined, interactive
  padding = 'medium', // small, medium, large, none
  margin = 'none', // small, medium, large, none
  onPress = null,
  style = {},
  ...props
}) => {
  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    // Variant styles
    switch (variant) {
      case 'elevated':
        baseStyle.push(styles.cardElevated);
        break;
      case 'outlined':
        baseStyle.push(styles.cardOutlined);
        break;
      case 'interactive':
        baseStyle.push(styles.cardInteractive);
        break;
      default:
        baseStyle.push(styles.cardDefault);
    }
    
    // Padding variants
    switch (padding) {
      case 'small':
        baseStyle.push(styles.paddingSmall);
        break;
      case 'large':
        baseStyle.push(styles.paddingLarge);
        break;
      case 'none':
        baseStyle.push(styles.paddingNone);
        break;
      default:
        baseStyle.push(styles.paddingMedium);
    }
    
    // Margin variants
    switch (margin) {
      case 'small':
        baseStyle.push(styles.marginSmall);
        break;
      case 'medium':
        baseStyle.push(styles.marginMedium);
        break;
      case 'large':
        baseStyle.push(styles.marginLarge);
        break;
      default:
        // No margin by default
        break;
    }
    
    return baseStyle;
  };
  
  const CardComponent = onPress ? TouchableOpacity : View;
  
  return (
    <CardComponent
      style={[...getCardStyle(), style]}
      onPress={onPress}
      activeOpacity={onPress ? 0.8 : 1}
      {...props}
    >
      {children}
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  // Base card styles
  card: {
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.cardBackground,
  },
  
  // Variant styles (restricted palette)
  cardDefault: {
    ...SHADOWS.small,
    borderWidth: 1,
    borderColor: COLORS.borderSubtle, // #1A102B
  },
  cardElevated: {
    ...SHADOWS.large,
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
    backgroundColor: COLORS.cardBackground, // #1A102B
  },
  cardOutlined: {
    borderWidth: 2,
    borderColor: COLORS.outline, // #6F2CFF
    backgroundColor: 'transparent',
    shadowOpacity: 0,
    elevation: 0,
  },
  cardInteractive: {
    ...SHADOWS.medium,
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
    backgroundColor: COLORS.cardBackground, // #1A102B
  },
  
  // Padding variants
  paddingNone: {
    padding: 0,
  },
  paddingSmall: {
    padding: SPACING.sm,
  },
  paddingMedium: {
    padding: SPACING.md,
  },
  paddingLarge: {
    padding: SPACING.lg,
  },
  
  // Margin variants
  marginSmall: {
    margin: SPACING.sm,
  },
  marginMedium: {
    margin: SPACING.md,
  },
  marginLarge: {
    margin: SPACING.lg,
  },
});

export default UnifiedCard;
