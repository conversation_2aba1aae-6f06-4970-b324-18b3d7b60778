// Chat History Modal for TestMeApp
// Uses the EXACT same visual design as Sources Registry modal

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../theme/colors';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ChatHistoryModal = ({ 
  visible, 
  onClose, 
  onSelectChat,
  onDeleteChat,
  title = "سجل المحادثات"
}) => {
  const [chatHistory, setChatHistory] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'oldest', 'messages'
  const [showFilters, setShowFilters] = useState(false);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    if (visible) {
      loadChatHistory();
      loadStats();
    }
  }, [visible]);

  useEffect(() => {
    filterAndSortHistory();
  }, [chatHistory, searchQuery, sortBy]);

  const loadChatHistory = async () => {
    try {
      setLoading(true);
      const savedHistory = await AsyncStorage.getItem('chat_history_list');
      const history = savedHistory ? JSON.parse(savedHistory) : [];
      setChatHistory(history);
    } catch (error) {
      console.error('Error loading chat history:', error);
      Alert.alert('خطأ', 'فشل في تحميل سجل المحادثات');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const savedHistory = await AsyncStorage.getItem('chat_history_list');
      const history = savedHistory ? JSON.parse(savedHistory) : [];
      
      const totalChats = history.length;
      const totalMessages = history.reduce((sum, chat) => sum + (chat.messageCount || 0), 0);
      const recentActivity = history.slice(0, 5).map(chat => ({
        title: chat.title,
        timestamp: chat.timestamp,
        messageCount: chat.messageCount
      }));

      setStats({
        totalChats,
        totalMessages,
        recentActivity
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const filterAndSortHistory = () => {
    let filtered = [...chatHistory];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat => 
        chat.title.toLowerCase().includes(query) ||
        chat.preview?.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.timestamp) - new Date(b.timestamp);
        case 'messages':
          return (b.messageCount || 0) - (a.messageCount || 0);
        case 'recent':
        default:
          return new Date(b.timestamp) - new Date(a.timestamp);
      }
    });

    setFilteredHistory(filtered);
  };

  const handleDeleteChat = async (chatId) => {
    Alert.alert(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذه المحادثة؟ لا يمكن التراجع عن هذا الإجراء.',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedHistory = chatHistory.filter(chat => chat.id !== chatId);
              setChatHistory(updatedHistory);
              await AsyncStorage.setItem('chat_history_list', JSON.stringify(updatedHistory));
              
              if (onDeleteChat) {
                onDeleteChat(chatId);
              }
            } catch (error) {
              console.error('Error deleting chat:', error);
              Alert.alert('خطأ', 'فشل في حذف المحادثة');
            }
          }
        }
      ]
    );
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return 'اليوم';
      } else if (diffDays === 2) {
        return 'أمس';
      } else if (diffDays <= 7) {
        return `منذ ${diffDays} أيام`;
      } else {
        return date.toLocaleDateString('ar-SA');
      }
    } catch (error) {
      return 'غير محدد';
    }
  };

  const ChatItem = ({ chat }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => onSelectChat && onSelectChat(chat)}
    >
      <View style={styles.chatHeader}>
        <View style={styles.chatInfo}>
          <MaterialIcons name="chat" size={24} color={COLORS.primary} />
          <View style={styles.chatDetails}>
            <Text style={styles.chatTitle} numberOfLines={1}>
              {chat.title}
            </Text>
            <Text style={styles.chatMeta}>
              {chat.messageCount || 0} رسالة • {formatDate(chat.timestamp)}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteChat(chat.id)}
        >
          <MaterialIcons name="delete" size={20} color={COLORS.error} />
        </TouchableOpacity>
      </View>

      {chat.preview && (
        <Text style={styles.chatPreview} numberOfLines={2}>
          {chat.preview}
        </Text>
      )}
    </TouchableOpacity>
  );

  const FilterChip = ({ label, value, isSelected, onPress }) => (
    <TouchableOpacity
      style={[styles.filterChip, isSelected && styles.filterChipSelected]}
      onPress={() => onPress(value)}
    >
      <Text style={[
        styles.filterChipText,
        isSelected && styles.filterChipTextSelected
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          
          <Text style={styles.title}>{title}</Text>
          
          <TouchableOpacity 
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <MaterialIcons name="filter-list" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </View>

        {/* Stats Bar */}
        {stats && (
          <View style={styles.statsBar}>
            <Text style={styles.statsText}>
              {stats.totalChats} محادثة • {stats.totalMessages} رسالة
            </Text>
          </View>
        )}

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color={COLORS.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="البحث في المحادثات..."
            placeholderTextColor={COLORS.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <MaterialIcons name="clear" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Filters */}
        {showFilters && (
          <View style={styles.filtersContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>ترتيب:</Text>
                <FilterChip
                  label="الأحدث"
                  value="recent"
                  isSelected={sortBy === 'recent'}
                  onPress={setSortBy}
                />
                <FilterChip
                  label="الأقدم"
                  value="oldest"
                  isSelected={sortBy === 'oldest'}
                  onPress={setSortBy}
                />
                <FilterChip
                  label="عدد الرسائل"
                  value="messages"
                  isSelected={sortBy === 'messages'}
                  onPress={setSortBy}
                />
              </View>
            </ScrollView>
          </View>
        )}

        {/* Chat History List */}
        <ScrollView style={styles.chatsList} showsVerticalScrollIndicator={false}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>جاري التحميل...</Text>
            </View>
          ) : filteredHistory.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="chat-bubble-outline" size={64} color={COLORS.textSecondary} />
              <Text style={styles.emptyText}>
                {searchQuery ? 'لا توجد نتائج للبحث' : 'لا توجد محادثات محفوظة'}
              </Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'جرب كلمات بحث أخرى' : 'ابدأ محادثة جديدة مع المساعد الذكي'}
              </Text>
            </View>
          ) : (
            filteredHistory.map(chat => (
              <ChatItem key={chat.id} chat={chat} />
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

export default ChatHistoryModal;
