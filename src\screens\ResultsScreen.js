import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  StatusBar,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Text,
  useTheme,
  Card,
  Title,
  Button,
  IconButton,
  Chip,
  Surface,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';

const ResultsScreen = ({ route, navigation }) => {
  const theme = useTheme();
  const { results } = route.params;
  const [showChatbot, setShowChatbot] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);

  const { questions, answers, quizData, timeSpent, completedAt } = results;

  // Calculate results
  const totalQuestions = questions.length;
  const correctAnswers = questions.filter(q => answers[q.id] === q.correctAnswer).length;
  const incorrectAnswers = totalQuestions - correctAnswers;
  const score = Math.round((correctAnswers / totalQuestions) * 100);

  useEffect(() => {
    saveQuizHistory();
  }, []);

  const saveQuizHistory = async () => {
    try {
      const historyKey = 'quiz_history';
      const existingHistory = await AsyncStorage.getItem(historyKey);
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      
      const newEntry = {
        id: Date.now().toString(),
        subject: quizData.subject,
        score,
        totalQuestions,
        correctAnswers,
        timeSpent,
        completedAt,
        results,
      };

      history.unshift(newEntry);
      
      // Keep only last 50 entries
      if (history.length > 50) {
        history.splice(50);
      }

      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
    } catch (error) {
      console.error('Error saving quiz history:', error);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins} دقيقة و ${secs} ثانية`;
  };

  const getScoreColor = () => {
    if (score >= 80) return '#10B981'; // Green
    if (score >= 60) return '#F59E0B'; // Yellow
    return '#EF4444'; // Red
  };

  const getScoreEmoji = () => {
    if (score >= 90) return '🏆';
    if (score >= 80) return '🎉';
    if (score >= 70) return '👍';
    if (score >= 60) return '😊';
    return '💪';
  };

  const handleChatWithAI = (question) => {
    setSelectedQuestion(question);
    setShowChatbot(true);
    // In a real app, this would open a chatbot interface
    Alert.alert(
      'شرح بالذكاء الاصطناعي',
      `سيتم فتح محادثة مع الذكاء الاصطناعي لشرح السؤال: "${question.question}"\n\nهذه الميزة ستكون متاحة في التحديث القادم.`,
      [{ text: 'حسناً' }]
    );
  };

  const handleRetakeQuiz = () => {
    Alert.alert(
      'إعادة الاختبار',
      'هل تريد إعادة نفس الاختبار؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        { 
          text: 'نعم', 
          onPress: () => navigation.navigate('Quiz', { quizData })
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back-ios" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Results</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Main Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Score Section */}
          <View style={styles.scoreSection}>
            <Text style={styles.scoreText}>
              {correctAnswers}/{totalQuestions}
            </Text>
            <Text style={styles.percentageText}>
              {score}%
            </Text>
          </View>

          {/* Progress Bar */}
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBackground}>
              <View
                style={[
                  styles.progressBarFill,
                  { width: `${score}%` }
                ]}
              />
            </View>
          </View>

          {/* Question Breakdown Header */}
          <Text style={styles.sectionTitle}>Question Breakdown</Text>

        {/* Quiz Info */}
        <Card style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
              معلومات الاختبار
            </Title>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                المادة:
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {quizData.subject}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                نوع الأسئلة:
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {quizData.questionType === 'multiple_choice' ? 'اختيار من متعدد' : 
                 quizData.questionType === 'true_false' ? 'صح أو خطأ' : 'مختلط'}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                تاريخ الإكمال:
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {new Date(completedAt).toLocaleDateString('ar-SA')}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Detailed Results */}
        <Card style={[styles.detailsCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.detailsTitle, { color: theme.colors.onSurface }]}>
              تفاصيل الإجابات
            </Title>

            {questions.map((question, index) => {
              const userAnswer = answers[question.id];
              const isCorrect = userAnswer === question.correctAnswer;
              
              return (
                <Surface
                  key={question.id}
                  style={[
                    styles.questionResult,
                    {
                      backgroundColor: isCorrect 
                        ? '#F0FDF4' 
                        : '#FEF2F2'
                    }
                  ]}
                >
                  <View style={styles.questionHeader}>
                    <View style={styles.questionInfo}>
                      <View style={styles.questionNumber}>
                        {isCorrect ? (
                          <CheckIcon size={20} color="#10B981" />
                        ) : (
                          <XIcon size={20} color="#EF4444" />
                        )}
                        <Text style={[styles.questionNumberText, { color: theme.colors.onSurface }]}>
                          {index + 1}
                        </Text>
                      </View>
                      <Text style={[styles.questionText, { color: theme.colors.onSurface }]}>
                        {question.question}
                      </Text>
                    </View>
                    
                    <IconButton
                      icon={() => <ChatIcon size={20} color={theme.colors.primary} />}
                      onPress={() => handleChatWithAI(question)}
                      style={styles.chatButton}
                    />
                  </View>

                  <View style={styles.answerSection}>
                    {userAnswer !== undefined && (
                      <View style={styles.answerRow}>
                        <Text style={[styles.answerLabel, { color: theme.colors.onSurfaceVariant }]}>
                          إجابتك:
                        </Text>
                        <Chip
                          style={[
                            styles.answerChip,
                            { backgroundColor: isCorrect ? '#10B981' : '#EF4444' }
                          ]}
                          textStyle={{ color: '#FFFFFF' }}
                        >
                          {question.options[userAnswer]}
                        </Chip>
                      </View>
                    )}

                    {!isCorrect && (
                      <View style={styles.answerRow}>
                        <Text style={[styles.answerLabel, { color: theme.colors.onSurfaceVariant }]}>
                          الإجابة الصحيحة:
                        </Text>
                        <Chip
                          style={[styles.answerChip, { backgroundColor: '#10B981' }]}
                          textStyle={{ color: '#FFFFFF' }}
                        >
                          {question.options[question.correctAnswer]}
                        </Chip>
                      </View>
                    )}

                    <View style={styles.explanationSection}>
                      <Text style={[styles.explanationTitle, { color: theme.colors.onSurface }]}>
                        التفسير:
                      </Text>
                      <Text style={[styles.explanationText, { color: theme.colors.onSurfaceVariant }]}>
                        {question.explanation}
                      </Text>
                      <Text style={[styles.referenceText, { color: theme.colors.primary }]}>
                        المرجع: {question.reference}
                      </Text>
                    </View>
                  </View>
                </Surface>
              );
            })}
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            mode="outlined"
            onPress={handleRetakeQuiz}
            style={[styles.actionButton, { borderColor: theme.colors.primary }]}
            labelStyle={{ color: theme.colors.primary }}
          >
            إعادة الاختبار
          </Button>

          <Button
            mode="contained"
            onPress={() => navigation.navigate('Home')}
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          >
            العودة للرئيسية
          </Button>
        </View>

      </ScrollView>
    </SafeAreaView>
  </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height * 0.3,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 20,
  },
  headerGradient: {
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.9,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  summaryContainer: {
    marginBottom: 24,
  },
  summaryCard: {
    borderRadius: 24,
    padding: 32,
  },
  chartSection: {
    alignItems: 'center',
    marginBottom: 32,
    position: 'relative',
  },
  chartCenter: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressTextContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 24,
    fontWeight: '700',
  },
  scoreEmoji: {
    marginBottom: 8,
  },
  scoreLabel: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  statsContainer: {
    marginTop: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  statCardGradient: {
    padding: 16,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
  },
  infoCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
  },
  detailsCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  questionResult: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  questionInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  questionNumber: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  questionNumberText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  questionText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 22,
  },
  chatButton: {
    margin: 0,
  },
  answerSection: {
    gap: 8,
  },
  answerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  answerChip: {
    maxWidth: '60%',
  },
  explanationSection: {
    marginTop: 12,
    padding: 12,
    backgroundColor: 'rgba(107, 70, 193, 0.05)',
    borderRadius: 8,
  },
  explanationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  referenceText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
  },
});

export default ResultsScreen;
