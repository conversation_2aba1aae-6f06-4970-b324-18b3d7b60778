import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  <PERSON><PERSON>,
  BackHandler,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  TextInput,
} from 'react-native';
import {
  Text,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';

// Import unified design system
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';

// Fallback mock questions if AI generation fails
const generateMockQuestions = (quizData) => {
  const questions = [];
  const count = quizData.questionCount;

  for (let i = 0; i < count; i++) {
    if (quizData.questionType === 'multiple_choice' || quizData.questionType === 'mixed') {
      questions.push({
        id: i + 1,
        type: 'multiple_choice',
        question: `ما هو الموضوع الرئيسي الذي تمت مناقشته في الوثيقة؟`,
        options: [
          'تاريخ الحضارات القديمة',
          'تأثير التكنولوجيا على المجتمع',
          'مبادئ فيزياء الكم',
          'فن الرسم في عصر النهضة'
        ],
        correctAnswer: 0,
        explanation: 'هذا هو التفسير الصحيح للإجابة من المصدر المرفوع.',
        reference: `صفحة ${Math.floor(Math.random() * 100) + 1}`
      });
    } else if (quizData.questionType === 'true_false') {
      questions.push({
        id: i + 1,
        type: 'true_false',
        question: `الشمس تدور حول الأرض.`,
        options: ['صح', 'خطأ'],
        correctAnswer: 1,
        explanation: 'هذا هو التفسير الصحيح للإجابة من المصدر المرفوع.',
        reference: `صفحة ${Math.floor(Math.random() * 100) + 1}`
      });
    }
  }

  return questions;
};

const QuizScreen = ({ route, navigation }) => {
  const theme = useTheme();
  const { quizData } = route.params;

  const [questions] = useState(() => {
    // Use AI-generated questions if available, otherwise use mock questions
    return quizData.questions && quizData.questions.length > 0
      ? quizData.questions
      : generateMockQuestions(quizData);
  });
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(quizData.timerMinutes * 60 || 600); // Convert to seconds
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [textAnswer, setTextAnswer] = useState('');

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          handleFinishQuiz();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Handle back button
  useEffect(() => {
    const backAction = () => {
      Alert.alert(
        'تأكيد الخروج',
        'هل أنت متأكد من الخروج؟ سيتم فقدان التقدم الحالي.',
        [
          { text: 'إلغاء', style: 'cancel' },
          { text: 'خروج', style: 'destructive', onPress: () => navigation.goBack() },
        ]
      );
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [navigation]);

  // Load saved answer when question changes
  useEffect(() => {
    setSelectedAnswer(answers[currentQuestion.id] || null);
  }, [currentQuestionIndex, answers]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (answerIndex) => {
    setSelectedAnswer(answerIndex);
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answerIndex
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      handleFinishQuiz();
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleFinishQuiz = () => {
    const results = {
      quizData,
      questions,
      answers,
      timeSpent: (quizData.timerMinutes * 60) - timeLeft,
      completedAt: new Date().toISOString(),
    };

    navigation.replace('Results', { results });
  };

  const getTimeColor = () => {
    if (timeLeft < 300) return theme.colors.error; // Less than 5 minutes
    if (timeLeft < 600) return '#FF9500'; // Less than 10 minutes
    return theme.colors.primary;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <MaterialIcons name="arrow-forward-ios" size={24} color={COLORS.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>عنوان الاختبار</Text>
            <TouchableOpacity style={styles.timerButton}>
              <MaterialIcons name="timer" size={24} color={COLORS.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Progress Section */}
          <View style={styles.progressSection}>
            <View style={styles.progressInfo}>
              <Text style={styles.progressLabel}>تقدم الاختبار</Text>
              <Text style={styles.progressPercentage}>{Math.round(progress)}%</Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBarFill, { width: `${progress}%` }]} />
            </View>
          </View>

          {/* Timer Display */}
          <View style={styles.timerDisplay}>
            <View style={styles.timerCard}>
              <Text style={styles.timerNumber}>{Math.floor(timeLeft / 60).toString().padStart(2, '0')}</Text>
              <Text style={styles.timerLabel}>دقائق</Text>
            </View>
            <View style={styles.timerCard}>
              <Text style={styles.timerNumber}>{(timeLeft % 60).toString().padStart(2, '0')}</Text>
              <Text style={styles.timerLabel}>ثواني</Text>
            </View>
          </View>
        </View>

        {/* Main Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Question Card */}
          <View style={styles.questionCard}>
            <View style={styles.questionHeader}>
              <Text style={styles.questionNumber}>السؤال {currentQuestionIndex + 1}</Text>
              <Text style={styles.questionType}>
                {currentQuestion.type === 'multiple_choice' ? 'اختيار من متعدد' :
                 currentQuestion.type === 'true_false' ? 'صح / خطأ' : 'إجابة قصيرة'}
              </Text>
            </View>
            <Text style={styles.questionText}>{currentQuestion.question}</Text>

            {/* Options */}
            {currentQuestion.type !== 'short_answer' ? (
              <View style={styles.optionsContainer}>
                {currentQuestion.options.map((option, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.optionCard,
                      selectedAnswer === index && styles.optionCardSelected
                    ]}
                    onPress={() => handleAnswerSelect(index)}
                  >
                    <Text style={styles.optionText}>{option}</Text>
                    <View style={[
                      styles.radioButton,
                      selectedAnswer === index && styles.radioButtonSelected
                    ]}>
                      {selectedAnswer === index && <View style={styles.radioButtonInner} />}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <TextInput
                style={styles.textAnswerInput}
                placeholder="اكتب إجابتك هنا..."
                placeholderTextColor={COLORS.textSecondary}
                value={textAnswer}
                onChangeText={setTextAnswer}
                multiline
                numberOfLines={3}
              />
            )}
          </View>
        </ScrollView>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleFinishQuiz}
          >
            <Text style={styles.submitButtonText}>إرسال الاختبار</Text>
          </TouchableOpacity>
        </View>


      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.background,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  backButton: {
    width: 48,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  timerButton: {
    width: 48,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  progressSection: {
    paddingVertical: 12,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: COLORS.secondary,
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 5,
  },
  timerDisplay: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 16,
  },
  timerCard: {
    flex: 1,
    backgroundColor: COLORS.secondary,
    borderRadius: 12,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  timerNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  timerLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 120,
    gap: 24,
  },
  questionCard: {
    backgroundColor: COLORS.secondary,
    borderRadius: 12,
    padding: 16,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  questionType: {
    fontSize: 12,
    color: COLORS.accent,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.textPrimary,
    lineHeight: 24,
    textAlign: 'right',
    marginBottom: 16,
  },
  optionsContainer: {
    gap: 12,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    gap: 16,
  },
  optionCardSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.selectedOption,
  },
  optionText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.textPrimary,
    textAlign: 'right',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: COLORS.primary,
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.primary,
  },
  textAnswerInput: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: COLORS.textPrimary,
    textAlign: 'right',
    minHeight: 80,
    textAlignVertical: 'top',
  },
  submitContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },

});

export default QuizScreen;
