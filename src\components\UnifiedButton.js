import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../theme/colors';

const UnifiedButton = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, outline, text
  size = 'medium', // small, medium, large
  disabled = false,
  loading = false,
  icon = null,
  style = {},
  textStyle = {},
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    // Size variants
    switch (size) {
      case 'small':
        baseStyle.push(styles.buttonSmall);
        break;
      case 'large':
        baseStyle.push(styles.buttonLarge);
        break;
      default:
        baseStyle.push(styles.buttonMedium);
    }
    
    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.buttonSecondary);
        break;
      case 'outline':
        baseStyle.push(styles.buttonOutline);
        break;
      case 'text':
        baseStyle.push(styles.buttonText);
        break;
      default:
        baseStyle.push(styles.buttonPrimary);
    }
    
    // Disabled state
    if (disabled) {
      baseStyle.push(styles.buttonDisabled);
    }
    
    return baseStyle;
  };
  
  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    // Size variants
    switch (size) {
      case 'small':
        baseStyle.push(styles.textSmall);
        break;
      case 'large':
        baseStyle.push(styles.textLarge);
        break;
      default:
        baseStyle.push(styles.textMedium);
    }
    
    // Variant text styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.textSecondary);
        break;
      case 'outline':
        baseStyle.push(styles.textOutline);
        break;
      case 'text':
        baseStyle.push(styles.textOnly);
        break;
      default:
        baseStyle.push(styles.textPrimary);
    }
    
    // Disabled text
    if (disabled) {
      baseStyle.push(styles.textDisabled);
    }
    
    return baseStyle;
  };
  
  return (
    <TouchableOpacity
      style={[...getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'primary' ? COLORS.onPrimary : COLORS.primary} 
        />
      ) : (
        <>
          {icon && icon}
          <Text style={[...getTextStyle(), textStyle]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Base button styles
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.small,
  },
  
  // Size variants
  buttonSmall: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 36,
  },
  buttonMedium: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    minHeight: 48,
  },
  buttonLarge: {
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
    minHeight: 56,
  },
  
  // Variant styles (restricted palette)
  buttonPrimary: {
    backgroundColor: COLORS.primary, // #6F2CFF
  },
  buttonSecondary: {
    backgroundColor: COLORS.cardBackground, // #1A102B
    borderWidth: 1,
    borderColor: COLORS.border, // #A88FDB
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: COLORS.primary, // #6F2CFF
  },
  buttonText: {
    backgroundColor: 'transparent',
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonDisabled: {
    backgroundColor: COLORS.surfaceDisabled, // #A88FDB
    opacity: 0.6,
  },
  
  // Text styles
  textSmall: {
    ...TYPOGRAPHY.labelMedium,
  },
  textMedium: {
    ...TYPOGRAPHY.labelLarge,
  },
  textLarge: {
    ...TYPOGRAPHY.titleMedium,
  },
  
  // Text variant styles (restricted palette)
  textPrimary: {
    color: COLORS.onPrimary, // #FFFFFF
    fontWeight: '600',
  },
  textSecondary: {
    color: COLORS.textPrimary, // #FFFFFF
    fontWeight: '600',
  },
  textOutline: {
    color: COLORS.primary, // #6F2CFF
    fontWeight: '600',
  },
  textOnly: {
    color: COLORS.primary, // #6F2CFF
    fontWeight: '500',
  },
  textDisabled: {
    color: COLORS.textDisabled, // #A88FDB
  },
});

export default UnifiedButton;
