import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

// Design System Colors (matching the provided design)
const COLORS = {
  background: '#000000',
  primary: '#8B5CF6', // Purple-500
  text: '#8B5CF6',
};

const WelcomeScreen = ({ navigation }) => {
  useEffect(() => {
    // Auto-navigate to Home after 3 seconds
    const timer = setTimeout(() => {
      navigation.replace('Home');
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.background} />

      <View style={styles.content}>
        {/* Large Check Circle Icon */}
        <MaterialIcons
          name="check-circle"
          size={160}
          color={COLORS.primary}
          style={styles.icon}
        />

        {/* App Title */}
        <Text style={styles.title}>اختبرني</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    fontSize: 56,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
    fontFamily: 'Cairo', // Will fallback to system font
  },
});

export default WelcomeScreen;
